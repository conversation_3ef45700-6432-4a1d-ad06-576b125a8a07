// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, implicit_dynamic_list_literal, implicit_dynamic_map_literal, inference_failure_on_collection_literal

import 'package:mason/mason.dart';

final flutterAppForMonoRepoBundle = MasonBundle.fromJson(<String, dynamic>{
  "files": [
    {
      "path": "{{ name.snakeCase() }}/CHANGELOG.md",
      "data": "IyMgMC4wLjEKCiogVE9ETzogRGVzY3JpYmUgaW5pdGlhbCByZWxlYXNlLg==",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/README.md",
      "data":
          "IyB7eyBuYW1lLnRpdGxlQ2FzZSgpIH19Cgp7eyBkZXNjcmlwdGlvbiB9fQoKWyFbbm9uc3RvcF9jbGldKGh0dHBzOi8vaW1nLnNoaWVsZHMuaW8vYmFkZ2Uvc3RhcnRlZCUyMHdpdGgtbm9uc3RvcF9jbGktMTY2QzRFLnN2Zz9zdHlsZT1mbGF0LXNxdWFyZSldKGh0dHBzOi8vcHViLmRldi9wYWNrYWdlcy9ub25zdG9wX2NsaSkKWyFbbWVsb3NdKGh0dHBzOi8vaW1nLnNoaWVsZHMuaW8vYmFkZ2UvbWFpbnRhaW5lZCUyMHdpdGgtbWVsb3MtZjcwMGZmLnN2Zz9zdHlsZT1mbGF0LXNxdWFyZSldKGh0dHBzOi8vZ2l0aHViLmNvbS9pbnZlcnRhc2UvbWVsb3MpCgojIyBJbnN0YWxsYXRpb24g8J+SuwoKKiog4pqg77iPIEluIG9yZGVyIHRvIHN0YXJ0IHVzaW5nIHt7bmFtZS50aXRsZUNhc2UoKX19IHlvdSBtdXN0IGhhdmUgdGhlIFtGbHV0dGVyIFNES11bZmx1dHRlcl9pbnN0YWxsX2xpbmtdIGluc3RhbGxlZCBvbiB5b3VyIG1hY2hpbmUuKioKCltmbHV0dGVyX2luc3RhbGxfbGlua106IGh0dHBzOi8vZG9jcy5mbHV0dGVyLmRldi9nZXQtc3RhcnRlZC9pbnN0YWxs",
      "type": "text"
    }
  ],
  "hooks": [
    {
      "path": "commands/flutter_app_create_command.dart",
      "data":
          "aW1wb3J0ICdwYWNrYWdlOmNsaV9jb3JlL2NsaV9jb3JlLmRhcnQnIHNob3cgQmFzZUZsdXR0ZXJDb21tYW5kOwppbXBvcnQgJ3BhY2thZ2U6bWFzb24vbWFzb24uZGFydCc7CgpmaW5hbCBjbGFzcyBGbHV0dGVyQXBwQ3JlYXRlQ29tbWFuZCBleHRlbmRzIEJhc2VGbHV0dGVyQ29tbWFuZCB7CiAgQG92ZXJyaWRlCiAgRnV0dXJlPHZvaWQ+IHJ1bihIb29rQ29udGV4dCBjb250ZXh0KSBhc3luYyB7CiAgICBmaW5hbCBTdHJpbmcgbmFtZSA9IGNvbnRleHQudmFyc1snbmFtZSddOwogICAgZmluYWwgU3RyaW5nIGRlc2NyaXB0aW9uID0gY29udGV4dC52YXJzWydkZXNjcmlwdGlvbiddOwogICAgZmluYWwgU3RyaW5nIG9yZ05hbWUgPSBjb250ZXh0LnZhcnNbJ29yZ19uYW1lJ10gPz8gJ2NvbS5leGFtcGxlJzsKICAgIGZpbmFsIGFwcE5hbWUgPSBuYW1lLnNuYWtlQ2FzZTsKCiAgICBhd2FpdCBjcmVhdGVGbHV0dGVyUHJvamVjdCgKICAgICAgY29udGV4dDogY29udGV4dCwKICAgICAgbmFtZTogYXBwTmFtZSwKICAgICAgZGVzY3JpcHRpb246IGRlc2NyaXB0aW9uLAogICAgICBvdXRwdXRQYXRoOiAnLicsCiAgICAgIG9yZ05hbWU6IG9yZ05hbWUsCiAgICApOwoKICAgIGZpbmFsIGlzTW9ub1JlcG8gPSBjb250ZXh0LnZhcnNbJ2lzX21vbm9fcmVwbyddID8/IGZhbHNlOwogICAgaWYgKGlzTW9ub1JlcG8pIHsKICAgICAgYXdhaXQgcmVtb3ZlQW5hbHlzaXNPcHRpb25zKAogICAgICAgIGNvbnRleHQ6IGNvbnRleHQsCiAgICAgICAgcHJvamVjdFBhdGg6IGFwcE5hbWUsCiAgICAgICk7CiAgICB9CiAgfQp9Cg==",
      "type": "text"
    },
    {
      "path": "post_gen.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmFzeW5jJzsKCmltcG9ydCAncGFja2FnZTptYXNvbi9tYXNvbi5kYXJ0JzsKCmltcG9ydCAnY29tbWFuZHMvZmx1dHRlcl9hcHBfY3JlYXRlX2NvbW1hbmQuZGFydCc7CgpGdXR1cmU8dm9pZD4gcnVuKEhvb2tDb250ZXh0IGNvbnRleHQpIGFzeW5jIHsKICBmaW5hbCBjb21tYW5kcyA9IFsKICAgIEZsdXR0ZXJBcHBDcmVhdGVDb21tYW5kKCksCiAgXTsKCiAgZm9yIChmaW5hbCBjb21tYW5kIGluIGNvbW1hbmRzKSB7CiAgICBhd2FpdCBjb21tYW5kLnJ1bihjb250ZXh0KTsKICB9Cn0K",
      "type": "text"
    },
    {
      "path": "pre_gen.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmFzeW5jJzsKCmltcG9ydCAncGFja2FnZTpjbGlfY29yZS9jbGlfY29yZS5kYXJ0JzsKaW1wb3J0ICdwYWNrYWdlOm1hc29uL21hc29uLmRhcnQnOwoKRnV0dXJlPHZvaWQ+IHJ1bihIb29rQ29udGV4dCBjb250ZXh0KSBhc3luYyB7CiAgLy8gQ2hlY2sgaWYgd2UncmUgaW4gYSBtb25vIHJlcG8gdXNpbmcgdGhlIHNoYXJlZCB1dGlsaXR5CiAgY29udGV4dC52YXJzWydpc19tb25vX3JlcG8nXSA9IGF3YWl0IEZpbGVVdGlscy5pc01vbm9SZXBvKCk7Cn0K",
      "type": "text"
    },
    {
      "path": "pubspec.yaml",
      "data":
          "bmFtZTogZmx1dHRlcl9hcHBfZm9yX21vbm9fcmVwb19ob29rcwoKZW52aXJvbm1lbnQ6CiAgc2RrOiAnPj0zLjAuMCA8NC4wLjAnCgpkZXBlbmRlbmNpZXM6CiAgbWFzb246IF4wLjEuMC1kZXYuNTkKICBtYXNvbl9sb2dnZXI6IF4wLjMuMQogIGNsaV9jb3JlOiBeMC4wLjItZGV2LjQ=",
      "type": "text"
    }
  ],
  "name": "flutter_app_for_mono_repo",
  "description": "A Flutter application for a Melos-managed mono-repo.",
  "version": "1.0.0",
  "environment": {"mason": ">=0.0.0-dev.52 <0.1.0"},
  "repository":
      "https://github.com/nonstopio/flutter_forge/tree/main/plugins/nonstop_cli",
  "readme": {
    "path": "README.md",
    "data":
        "IyBmbHV0dGVyX2FwcF9mb3JfbW9ub19yZXBvCgpBIGJyaWNrIHRvIGNyZWF0ZSBhIEZsdXR0ZXIgYXBwbGljYXRpb24gZm9yIGEgTWVsb3MtbWFuYWdlZCBtb25vLXJlcG8uCgojIyBGZWF0dXJlcwoKLSBDcmVhdGVzIGEgRmx1dHRlciBhcHBsaWNhdGlvbiB1c2luZyBgZmx1dHRlciBjcmVhdGVgCi0gQ29uZmlndXJlcyB0aGUgYXBwbGljYXRpb24gZm9yIGEgbW9uby1yZXBvIHN0cnVjdHVyZQotIFJlbW92ZXMgYW5hbHlzaXNfb3B0aW9ucy55YW1sIHRvIHVzZSB0aGUgcm9vdCBvbmUgZnJvbSBtb25vLXJlcG8KLSBTZXRzIHVwIHByb3BlciBmb2xkZXIgc3RydWN0dXJlIGZvciB0aGUgYXBwCgojIyBVc2FnZQoKYGBgc2hlbGwKbm9uc3RvcCBjcmVhdGUgbXlfcHJvamVjdCAtLXRlbXBsYXRlPWFwcApgYGAKCiMjIFZhcmlhYmxlcwoKfCBWYXJpYWJsZSAgICAgIHwgRGVzY3JpcHRpb24gICAgICAgICAgICAgICAgICAgICAgIHwgRGVmYXVsdCAgICAgICB8IFR5cGUgICAgIHwKfC0tLS0tLS0tLS0tLS0tLXwtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLXwtLS0tLS0tLS0tLS0tLS18LS0tLS0tLS0tLXwKfCBgbmFtZWAgICAgICAgIHwgVGhlIG5hbWUgb2YgdGhlIHByb2plY3QgICAgICAgICAgIHwgLSAgICAgICAgICAgICB8IGBzdHJpbmdgIHwKfCBgZGVzY3JpcHRpb25gIHwgVGhlIGRlc2NyaXB0aW9uIG9mIHRoZSBwcm9qZWN0ICAgIHwgLSAgICAgICAgICAgICB8IGBzdHJpbmdgIHwKfCBgb3JnX25hbWVgICAgIHwgVGhlIG9yZ2FuaXphdGlvbiBuYW1lIGZvciB0aGUgYXBwIHwgYGNvbS5leGFtcGxlYCB8IGBzdHJpbmdgIHw=",
    "type": "text"
  },
  "changelog": {
    "path": "CHANGELOG.md",
    "data":
        "IyAxLjAuMAoKLSBJbml0aWFsIHJlbGVhc2UgZm9yIGJ1aWxkaW5nIGEgRmx1dHRlciBBcHBsaWNhdGlvbiBmb3IgTW9uby1yZXBv",
    "type": "text"
  },
  "license": {
    "path": "LICENSE",
    "data":
        "TUlUIExpY2Vuc2UKCkNvcHlyaWdodCAoYykgMjAyNSBOb25TdG9wIGlvIFRlY2hub2xvZ2llcyBQdnQuIEx0ZC4KClBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHkgb2YKdGhpcyBzb2Z0d2FyZSBhbmQgYXNzb2NpYXRlZCBkb2N1bWVudGF0aW9uIGZpbGVzICh0aGUgIlNvZnR3YXJlIiksIHRvIGRlYWwgaW4KdGhlIFNvZnR3YXJlIHdpdGhvdXQgcmVzdHJpY3Rpb24sIGluY2x1ZGluZyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0cyB0bwp1c2UsIGNvcHksIG1vZGlmeSwgbWVyZ2UsIHB1Ymxpc2gsIGRpc3RyaWJ1dGUsIHN1YmxpY2Vuc2UsIGFuZC9vciBzZWxsIGNvcGllcyBvZgp0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpcyBmdXJuaXNoZWQgdG8gZG8gc28sCnN1YmplY3QgdG8gdGhlIGZvbGxvd2luZyBjb25kaXRpb25zOgoKVGhlIGFib3ZlIGNvcHlyaWdodCBub3RpY2UgYW5kIHRoaXMgcGVybWlzc2lvbiBub3RpY2Ugc2hhbGwgYmUgaW5jbHVkZWQgaW4gYWxsCmNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuCgpUSEUgU09GVFdBUkUgSVMgUFJPVklERUQgIkFTIElTIiwgV0lUSE9VVCBXQVJSQU5UWSBPRiBBTlkgS0lORCwgRVhQUkVTUyBPUgpJTVBMSUVELCBJTkNMVURJTkcgQlVUIE5PVCBMSU1JVEVEIFRPIFRIRSBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWSwgRklUTkVTUwpGT1IgQSBQQVJUSUNVTEFSIFBVUlBPU0UgQU5EIE5PTklORlJJTkdFTUVOVC4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUlMgT1IKQ09QWVJJR0hUIEhPTERFUlMgQkUgTElBQkxFIEZPUiBBTlkgQ0xBSU0sIERBTUFHRVMgT1IgT1RIRVIgTElBQklMSVRZLCBXSEVUSEVSCklOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgVE9SVCBPUiBPVEhFUldJU0UsIEFSSVNJTkcgRlJPTSwgT1VUIE9GIE9SIElOCkNPTk5FQ1RJT04gV0lUSCBUSEUgU09GVFdBUkUgT1IgVEhFIFVTRSBPUiBPVEhFUiBERUFMSU5HUyBJTiBUSEUgU09GVFdBUkUu",
    "type": "text"
  },
  "vars": {
    "name": {
      "type": "string",
      "description": "Name of the project",
      "prompt": "What is your project's name?"
    },
    "description": {
      "type": "string",
      "description": "Description of the project",
      "prompt": "What is your project's description?"
    },
    "org_name": {
      "type": "string",
      "description": "Organization name",
      "default": "com.example",
      "prompt": "What is your organization name?"
    }
  }
});
