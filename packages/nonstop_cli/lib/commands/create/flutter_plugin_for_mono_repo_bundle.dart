// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, implicit_dynamic_list_literal, implicit_dynamic_map_literal, inference_failure_on_collection_literal

import 'package:mason/mason.dart';

final flutterPluginForMonoRepoBundle = MasonBundle.fromJson(<String, dynamic>{
  "files": [
    {
      "path": "{{name.snakeCase()}}/CHANGELOG.md",
      "data": "IyMgMC4wLjEKCiogVE9ETzogRGVzY3JpYmUgaW5pdGlhbCByZWxlYXNlLg==",
      "type": "text"
    },
    {
      "path": "{{name.snakeCase()}}/README.md",
      "data":
          "IyB7eyBuYW1lLnRpdGxlQ2FzZSgpIH19Cgp7eyBkZXNjcmlwdGlvbiB9fQoKWyFbbm9uc3RvcF9jbGldKGh0dHBzOi8vaW1nLnNoaWVsZHMuaW8vYmFkZ2Uvc3RhcnRlZCUyMHdpdGgtbm9uc3RvcF9jbGktMTY2QzRFLnN2Zz9zdHlsZT1mbGF0LXNxdWFyZSldKGh0dHBzOi8vcHViLmRldi9wYWNrYWdlcy9ub25zdG9wX2NsaSkKWyFbbWVsb3NdKGh0dHBzOi8vaW1nLnNoaWVsZHMuaW8vYmFkZ2UvbWFpbnRhaW5lZCUyMHdpdGgtbWVsb3MtZjcwMGZmLnN2Zz9zdHlsZT1mbGF0LXNxdWFyZSldKGh0dHBzOi8vZ2l0aHViLmNvbS9pbnZlcnRhc2UvbWVsb3MpCgojIyBJbnN0YWxsYXRpb24g8J+SuwoKKirimqDvuI8gSW4gb3JkZXIgdG8gc3RhcnQgdXNpbmcge3tuYW1lLnRpdGxlQ2FzZSgpfX0geW91IG11c3QgaGF2ZSB0aGUgW0ZsdXR0ZXIgU0RLXVtmbHV0dGVyX2luc3RhbGxfbGlua10gaW5zdGFsbGVkIG9uIHlvdXIgbWFjaGluZS4qKgoKSW5zdGFsbCB2aWEgYGRhcnQgcHViIGFkZGA6CgpgYGBzaApkYXJ0IHB1YiBhZGQge3tuYW1lLnNuYWtlQ2FzZSgpfX0KYGBgCgpbZmx1dHRlcl9pbnN0YWxsX2xpbmtdOiBodHRwczovL2RvY3MuZmx1dHRlci5kZXYvZ2V0LXN0YXJ0ZWQvaW5zdGFsbA==",
      "type": "text"
    }
  ],
  "hooks": [
    {
      "path": "commands/flutter_plugin_create_command.dart",
      "data":
          "aW1wb3J0ICdwYWNrYWdlOmNsaV9jb3JlL2NsaV9jb3JlLmRhcnQnIHNob3cgQmFzZUZsdXR0ZXJDb21tYW5kOwppbXBvcnQgJ3BhY2thZ2U6bWFzb24vbWFzb24uZGFydCc7CgpmaW5hbCBjbGFzcyBGbHV0dGVyUGx1Z2luQ3JlYXRlQ29tbWFuZCBleHRlbmRzIEJhc2VGbHV0dGVyQ29tbWFuZCB7CiAgQG92ZXJyaWRlCiAgRnV0dXJlPHZvaWQ+IHJ1bihIb29rQ29udGV4dCBjb250ZXh0KSBhc3luYyB7CiAgICBmaW5hbCBTdHJpbmcgbmFtZSA9IGNvbnRleHQudmFyc1snbmFtZSddOwogICAgZmluYWwgU3RyaW5nIGRlc2NyaXB0aW9uID0gY29udGV4dC52YXJzWydkZXNjcmlwdGlvbiddOwogICAgZmluYWwgYXBwTmFtZSA9IG5hbWUuc25ha2VDYXNlOwoKICAgIGF3YWl0IGNyZWF0ZUZsdXR0ZXJQcm9qZWN0KAogICAgICBjb250ZXh0OiBjb250ZXh0LAogICAgICBuYW1lOiBhcHBOYW1lLAogICAgICBkZXNjcmlwdGlvbjogZGVzY3JpcHRpb24sCiAgICAgIG91dHB1dFBhdGg6ICcuJywKICAgICAgdGVtcGxhdGU6ICdwbHVnaW4nLAogICAgKTsKCiAgICBmaW5hbCBpc01vbm9SZXBvID0gY29udGV4dC52YXJzWydpc19tb25vX3JlcG8nXSA/PyBmYWxzZTsKICAgIGlmIChpc01vbm9SZXBvKSB7CiAgICAgIGF3YWl0IHJlbW92ZUFuYWx5c2lzT3B0aW9ucygKICAgICAgICBjb250ZXh0OiBjb250ZXh0LAogICAgICAgIHByb2plY3RQYXRoOiBhcHBOYW1lLAogICAgICApOwogICAgfQogIH0KfQo=",
      "type": "text"
    },
    {
      "path": "post_gen.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmFzeW5jJzsKCmltcG9ydCAncGFja2FnZTptYXNvbi9tYXNvbi5kYXJ0JzsKCmltcG9ydCAnY29tbWFuZHMvZmx1dHRlcl9wbHVnaW5fY3JlYXRlX2NvbW1hbmQuZGFydCc7CgpGdXR1cmU8dm9pZD4gcnVuKEhvb2tDb250ZXh0IGNvbnRleHQpIGFzeW5jIHsKICBmaW5hbCBjb21tYW5kcyA9IFsKICAgIEZsdXR0ZXJQbHVnaW5DcmVhdGVDb21tYW5kKCksCiAgXTsKCiAgZm9yIChmaW5hbCBjb21tYW5kIGluIGNvbW1hbmRzKSB7CiAgICBhd2FpdCBjb21tYW5kLnJ1bihjb250ZXh0KTsKICB9Cn0K",
      "type": "text"
    },
    {
      "path": "pre_gen.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmFzeW5jJzsKCmltcG9ydCAncGFja2FnZTpjbGlfY29yZS9jbGlfY29yZS5kYXJ0JzsKaW1wb3J0ICdwYWNrYWdlOm1hc29uL21hc29uLmRhcnQnOwoKRnV0dXJlPHZvaWQ+IHJ1bihIb29rQ29udGV4dCBjb250ZXh0KSBhc3luYyB7CiAgLy8gQ2hlY2sgaWYgd2UncmUgaW4gYSBtb25vIHJlcG8gdXNpbmcgdGhlIHNoYXJlZCB1dGlsaXR5CiAgY29udGV4dC52YXJzWydpc19tb25vX3JlcG8nXSA9IGF3YWl0IEZpbGVVdGlscy5pc01vbm9SZXBvKCk7Cn0K",
      "type": "text"
    },
    {
      "path": "pubspec.yaml",
      "data":
          "bmFtZTogZmx1dHRlcl9wbHVnaW5fZm9yX21vbm9fcmVwb19ob29rcwoKZW52aXJvbm1lbnQ6CiAgc2RrOiAnPj0zLjAuMCA8NC4wLjAnCgpkZXBlbmRlbmNpZXM6CiAgbWFzb246IF4wLjEuMC1kZXYuNTkKICBtYXNvbl9sb2dnZXI6IF4wLjMuMQogIGNsaV9jb3JlOiBeMC4wLjItZGV2LjQ=",
      "type": "text"
    }
  ],
  "name": "flutter_plugin_for_mono_repo",
  "description": "A Flutter plugin for a Melos-managed mono-repo.",
  "version": "1.0.0",
  "environment": {"mason": ">=0.1.0-dev.52 <0.1.0"},
  "repository":
      "https://github.com/nonstopio/flutter_forge/tree/main/plugins/nonstop_cli",
  "readme": {
    "path": "README.md",
    "data": "IyBmbHV0dGVyX3BsdWdpbl9mb3JfbW9ub19yZXBv",
    "type": "text"
  },
  "changelog": {
    "path": "CHANGELOG.md",
    "data":
        "IyAxLjAuMAoKLSBJbml0aWFsIHJlbGVhc2UgZm9yIGJ1aWxkaW5nIGEgRmx1dHRlciBQbHVnaW4gZm9yIE1vbm8tcmVwbw==",
    "type": "text"
  },
  "license": {
    "path": "LICENSE",
    "data":
        "TUlUIExpY2Vuc2UKCkNvcHlyaWdodCAoYykgMjAyNSBOb25TdG9wIGlvIFRlY2hub2xvZ2llcyBQdnQuIEx0ZC4KClBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHkgb2YKdGhpcyBzb2Z0d2FyZSBhbmQgYXNzb2NpYXRlZCBkb2N1bWVudGF0aW9uIGZpbGVzICh0aGUgIlNvZnR3YXJlIiksIHRvIGRlYWwgaW4KdGhlIFNvZnR3YXJlIHdpdGhvdXQgcmVzdHJpY3Rpb24sIGluY2x1ZGluZyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0cyB0bwp1c2UsIGNvcHksIG1vZGlmeSwgbWVyZ2UsIHB1Ymxpc2gsIGRpc3RyaWJ1dGUsIHN1YmxpY2Vuc2UsIGFuZC9vciBzZWxsIGNvcGllcyBvZgp0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpcyBmdXJuaXNoZWQgdG8gZG8gc28sCnN1YmplY3QgdG8gdGhlIGZvbGxvd2luZyBjb25kaXRpb25zOgoKVGhlIGFib3ZlIGNvcHlyaWdodCBub3RpY2UgYW5kIHRoaXMgcGVybWlzc2lvbiBub3RpY2Ugc2hhbGwgYmUgaW5jbHVkZWQgaW4gYWxsCmNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuCgpUSEUgU09GVFdBUkUgSVMgUFJPVklERUQgIkFTIElTIiwgV0lUSE9VVCBXQVJSQU5UWSBPRiBBTlkgS0lORCwgRVhQUkVTUyBPUgpJTVBMSUVELCBJTkNMVURJTkcgQlVUIE5PVCBMSU1JVEVEIFRPIFRIRSBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWSwgRklUTkVTUwpGT1IgQSBQQVJUSUNVTEFSIFBVUlBPU0UgQU5EIE5PTklORlJJTkdFTUVOVC4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUlMgT1IKQ09QWVJJR0hUIEhPTERFUlMgQkUgTElBQkxFIEZPUiBBTlkgQ0xBSU0sIERBTUFHRVMgT1IgT1RIRVIgTElBQklMSVRZLCBXSEVUSEVSCklOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgVE9SVCBPUiBPVEhFUldJU0UsIEFSSVNJTkcgRlJPTSwgT1VUIE9GIE9SIElOCkNPTk5FQ1RJT04gV0lUSCBUSEUgU09GVFdBUkUgT1IgVEhFIFVTRSBPUiBPVEhFUiBERUFMSU5HUyBJTiBUSEUgU09GVFdBUkUu",
    "type": "text"
  },
  "vars": {
    "name": {
      "type": "string",
      "description": "Name of the project",
      "prompt": "What is your project's name?"
    },
    "description": {
      "type": "string",
      "description": "Description of the project",
      "prompt": "What is your project's description?"
    }
  }
});
