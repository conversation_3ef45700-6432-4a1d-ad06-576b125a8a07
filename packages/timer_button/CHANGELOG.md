## 2.3.1

 - **DOCS**: add NonStop branding to README files across project.

## 2.3.0

 - **FEAT**(connectivity_wrapper): Moved `connectivity_wrapper` to mono repo.

## 2.2.0

 - **FEAT**: Setting up the mono repo for all the dart/flutter packages i.e flutter_forge.
 - **DOCS**(timer_button): Update README with new badge and formatting improvements.

## [2.1.1]

- Updated Documentation and Code Refactoring as per the dart analysis
- Updated `ButtonType` enum values to lowerSnakeCase
  
## [2.1.0]

- Resolved #13, #16 #17
- Code Refactoring and updated dependencies
- Thanks to all the contributors for their valuable contribution
    - [<PERSON><PERSON>](https://github.com/Adakar)
    - [Priyanshu](https://github.com/Priyanshu-Kashyap)
    - [AndresCreator](https://github.com/AndresCreator)
    - [CPrimbee](https://github.com/CPrimbee)
    - [udcode](https://github.com/udcode)
    - [chetanx<PERSON><PERSON>](https://github.com/chetanxpatil)

## [2.0.1]

- Updated Readme.md

## [2.0.1]

- Updated Readme.md

## [2.0.0]

* Migrating to null safety and Added New buttons. Thanks to [Adakar](https://github.com/Adakar)
* Feature Added: "secPostFix" pass it into the
  constructor ([#7](https://github.com/ProjectAJ14/timer_button/issues/7)). Thanks
  to [evanholt1](https://github.com/evanholt1)

## [1.0.2]

* Feature Added: Reset timer button ([#5](https://github.com/ProjectAJ14/timer_button/issues/5)).
  Thanks to [gerald-tetteh](https://github.com/gerald-tetteh)

## [1.0.1]

* Updated package description

## [1.0.0]

* Updated environment >=2.7.0 <3.0.0

## [0.0.6]

* Minor fixes.

## [0.0.5]

* Updated Example and Documents.

## [0.0.4]

* Minor fixes.

## [0.0.3]

* Updated Example and Documents.

## [0.0.2]

* Added Example

## [0.0.1]

* Initial.