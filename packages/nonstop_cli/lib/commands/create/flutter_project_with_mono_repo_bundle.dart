// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, implicit_dynamic_list_literal, implicit_dynamic_map_literal, inference_failure_on_collection_literal

import 'package:mason/mason.dart';

final flutterProjectWithMonoRepoBundle = MasonBundle.fromJson(<String, dynamic>{
  "files": [
    {
      "path": "{{ name.snakeCase() }}/.gitignore",
      "data":
          "Kiovbm9kZV9tb2R1bGVzCioubG9nCmJ1aWxkCmRpc3QKLnNhbml0eQouRFNfU3RvcmUKLmlkZWEKY292ZXJhZ2UKLmRhcnRfdG9vbApwdWJzcGVjX292ZXJyaWRlcy55YW1sCi5mbHV0dGVyLXBsdWdpbnMKLmZsdXR0ZXItcGx1Z2lucy1kZXBlbmRlbmNpZXMKKi5pbWwKKi5sb2NrCm1hc29uLWxvY2suanNvbgoubWFzb24K",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/analysis_options.yaml",
      "data":
          "aW5jbHVkZTogcGFja2FnZTpmbHV0dGVyX2xpbnRzL2ZsdXR0ZXIueWFtbAoKbGludGVyOgogIHJ1bGVzOgogICAgcmVxdWlyZV90cmFpbGluZ19jb21tYXM6IHRydWU=",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/apps/README.md",
      "data": "IyBQdXQgYWxsIHlvdXIgYXBwcyBpbiB0aGlzIGZvbGRlcgo=",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/features/README.md",
      "data": "IyBQdXQgYWxsIHlvdXIgZmVhdHVyZXMgaW4gdGhpcyBmb2xkZXI=",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/melos.yaml",
      "data":
          "bmFtZToge3sgbmFtZS5zbmFrZUNhc2UoKSB9fV93b3Jrc3BhY2UKCiMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMKIyBNT05PUkVQTyBDT05GSUdVUkFUSU9OCiMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMKIyBEZWZpbmUgdGhlIHBhY2thZ2VzIHRoYXQgbWFrZSB1cCB5b3VyIG1vbm9yZXBvCiMgQ3VzdG9taXplIHRoZXNlIHBhdHRlcm5zIHRvIG1hdGNoIHlvdXIgcHJvamVjdCBzdHJ1Y3R1cmUKcGFja2FnZXM6CiAgLSBhcHBzLyoKICAtIGZlYXR1cmVzLyoKICAtIHBhY2thZ2VzLyoKICAtIHBsdWdpbnMvKgoKIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIwojIFNDUklQVFMKIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIwpzY3JpcHRzOgogIGNsZWFuOmZsdXR0ZXI6CiAgICBkZXNjcmlwdGlvbjogIlJ1biBmbHV0dGVyIGNsZWFuIGluIGFsbCBGbHV0dGVyIHBhY2thZ2VzIgogICAgcnVuOiBmbHV0dGVyIGNsZWFuCiAgICBleGVjOgogICAgICBjb25jdXJyZW5jeTogNAogICAgcGFja2FnZUZpbHRlcnM6CiAgICAgIGZsdXR0ZXI6IHRydWUKCiAgbGludDoKICAgIGRlc2NyaXB0aW9uOiAiUnVuIGZvcm1hdCBhbmQgYW5hbHl6ZSBhbGwgcGFja2FnZXMiCiAgICBydW46IHwKICAgICAgZGFydCBmb3JtYXQgLS1zZXQtZXhpdC1pZi1jaGFuZ2VkIC4gJiYKICAgICAgZGFydCBhbmFseXplIC0tZmF0YWwtaW5mb3MgLgogICAgZXhlYzoKICAgICAgY29uY3VycmVuY3k6IDUKICAgICAgZmFpbEZhc3Q6IHRydWUKCiAgZml4OgogICAgZGVzY3JpcHRpb246ICJBcHBseSBEYXJ0IGF1dG9tYXRlZCBmaXhlcyB0byBhbGwgcGFja2FnZXMiCiAgICBydW46IGRhcnQgZml4IC0tYXBwbHkKICAgIGV4ZWM6CiAgICAgIGNvbmN1cnJlbmN5OiA1CiAgICBwYWNrYWdlRmlsdGVyczoKICAgICAgZGlyRXhpc3RzOiBsaWIKCiAgZml4OmRyeToKICAgIGRlc2NyaXB0aW9uOiAiU2hvdyBEYXJ0IGF1dG9tYXRlZCBmaXhlcyB3aXRob3V0IGFwcGx5aW5nIHRoZW0iCiAgICBydW46IGRhcnQgZml4IC0tZHJ5LXJ1bgogICAgZXhlYzoKICAgICAgY29uY3VycmVuY3k6IDUKICAgIHBhY2thZ2VGaWx0ZXJzOgogICAgICBkaXJFeGlzdHM6IGxpYgoKICBnZW5lcmF0ZToKICAgIGRlc2NyaXB0aW9uOiAiR2VuZXJhdGUgY29kZSBmb3IgRGFydCBwYWNrYWdlcyB1c2luZyBidWlsZF9ydW5uZXIiCiAgICBydW46IGRhcnQgcnVuIGJ1aWxkX3J1bm5lciBidWlsZCAtLWRlbGV0ZS1jb25mbGljdGluZy1vdXRwdXRzCiAgICBleGVjOgogICAgICBjb25jdXJyZW5jeTogMQogICAgcGFja2FnZUZpbHRlcnM6CiAgICAgIGRlcGVuZHNPbjogYnVpbGRfcnVubmVyCgogIHRlc3Q6CiAgICBkZXNjcmlwdGlvbjogIlJ1biBhbGwgdGVzdHMgKERhcnQgYW5kIEZsdXR0ZXIpIgogICAgcnVuOiBtZWxvcyBydW4gdGVzdDpzZWxlY3QgLS1uby1zZWxlY3QKCiAgdGVzdDpzZWxlY3Q6CiAgICBkZXNjcmlwdGlvbjogIlJ1biB0ZXN0cyBpbiBGbHV0dGVyIHBhY2thZ2VzIgogICAgcnVuOiBmbHV0dGVyIHRlc3QgLS1jb3ZlcmFnZQogICAgZXhlYzoKICAgICAgY29uY3VycmVuY3k6IDUKICAgICAgZmFpbEZhc3Q6IHRydWUKICAgIHBhY2thZ2VGaWx0ZXJzOgogICAgICBkaXJFeGlzdHM6IHRlc3QKCiMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMKIyBJREUgQ09ORklHVVJBVElPTgojIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjCiMgT3B0aW9uYWw6IENvbmZpZ3VyZSBJREUgaW50ZWdyYXRpb24KaWRlOgogIGludGVsbGlqOgogICAgZW5hYmxlZDogdHJ1ZQogICAgbW9kdWxlTmFtZVByZWZpeDogJyc=",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/packages/README.md",
      "data":
          "IyBQdXQgYWxsIHlvdXIgc2hhcmVkIHBhY2thZ2VzIGluIHRoaXMgZm9sZGVyCg==",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/plugins/README.md",
      "data": "IyBQdXQgYWxsIHlvdXIgcGx1Z2lucyBpbiB0aGlzIGZvbGRlcgo=",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/pubspec.yaml",
      "data":
          "bmFtZToge3sgbmFtZS5zbmFrZUNhc2UoKSB9fQpkZXNjcmlwdGlvbjoge3sgZGVzY3JpcHRpb24gfX0KCmVudmlyb25tZW50OgogIHNkazogJz49My4wLjAgPDQuMC4wJwoKZGV2X2RlcGVuZGVuY2llczoKICBmbHV0dGVyX2xpbnRzOiBeNS4wLjAKICBtZWxvczogXjYuMi4wCg==",
      "type": "text"
    },
    {
      "path": "{{ name.snakeCase() }}/README.md",
      "data":
          "IyB7eyBuYW1lLnRpdGxlQ2FzZSgpIH19Cgp7eyBkZXNjcmlwdGlvbiB9fQoKWyFbbm9uc3RvcF9jbGldKGh0dHBzOi8vaW1nLnNoaWVsZHMuaW8vYmFkZ2Uvc3RhcnRlZCUyMHdpdGgtbm9uc3RvcF9jbGktMTY2QzRFLnN2Zz9zdHlsZT1mbGF0LXNxdWFyZSldKGh0dHBzOi8vcHViLmRldi9wYWNrYWdlcy9ub25zdG9wX2NsaSkKWyFbbWVsb3NdKGh0dHBzOi8vaW1nLnNoaWVsZHMuaW8vYmFkZ2UvbWFpbnRhaW5lZCUyMHdpdGgtbWVsb3MtZjcwMGZmLnN2Zz9zdHlsZT1mbGF0LXNxdWFyZSldKGh0dHBzOi8vZ2l0aHViLmNvbS9pbnZlcnRhc2UvbWVsb3MpCgoKIyMgVGVjaG5vbG9naWVzIGF0IHBsYXkKCi0gRGFydAotIEZsdXR0ZXIKLSBNZWxvcwoKIyMgR2V0dGluZyBTdGFydGVkCgotIEFsbCBhcHBzIGFyZSBpbiB0aGUgYC9hcHBzYCBkaXJlY3RvcnkuCi0gQWxsIGZlYXR1cmVzIGFyZSBpbiB0aGUgYC9mZWF0dXJlc2AgZGlyZWN0b3J5Ci0gQWxsIHNoYXJlZCBwYWNrYWdlcyBhcmUgaW4gdGhlIGAvcGFja2FnZXNgIGRpcmVjdG9yeQotIEFsbCBwbHVnaW5zIGFyZSBpbiB0aGUgYC9wbHVnaW5zYCBkaXJlY3Rvcnk=",
      "type": "text"
    }
  ],
  "hooks": [
    {
      "path": "commands/flutter_create_command.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmFzeW5jJzsKCmltcG9ydCAncGFja2FnZTpjbGlfY29yZS9jbGlfY29yZS5kYXJ0JyBzaG93IEJhc2VGbHV0dGVyQ29tbWFuZDsKaW1wb3J0ICdwYWNrYWdlOm1hc29uL21hc29uLmRhcnQnOwppbXBvcnQgJ3BhY2thZ2U6cGF0aC9wYXRoLmRhcnQnIGFzIHA7CgpmaW5hbCBjbGFzcyBGbHV0dGVyQ3JlYXRlQ29tbWFuZCBleHRlbmRzIEJhc2VGbHV0dGVyQ29tbWFuZCB7CiAgQG92ZXJyaWRlCiAgRnV0dXJlPHZvaWQ+IHJ1bihIb29rQ29udGV4dCBjb250ZXh0KSBhc3luYyB7CiAgICBmaW5hbCBTdHJpbmcgbmFtZSA9IGNvbnRleHQudmFyc1snbmFtZSddOwogICAgZmluYWwgU3RyaW5nIGRlc2NyaXB0aW9uID0gY29udGV4dC52YXJzWydkZXNjcmlwdGlvbiddOwogICAgZmluYWwgYXBwTmFtZSA9IG5hbWUuc25ha2VDYXNlOwogICAgZmluYWwgb3V0cHV0UGF0aCA9IHAubm9ybWFsaXplKCckYXBwTmFtZS9hcHBzJyk7CgogICAgYXdhaXQgY3JlYXRlRmx1dHRlclByb2plY3QoCiAgICAgIGNvbnRleHQ6IGNvbnRleHQsCiAgICAgIG5hbWU6IGFwcE5hbWUsCiAgICAgIGRlc2NyaXB0aW9uOiBkZXNjcmlwdGlvbiwKICAgICAgb3V0cHV0UGF0aDogb3V0cHV0UGF0aCwKICAgICk7CgogICAgYXdhaXQgcmVtb3ZlQW5hbHlzaXNPcHRpb25zKAogICAgICBjb250ZXh0OiBjb250ZXh0LAogICAgICBwcm9qZWN0UGF0aDogcC5qb2luKG91dHB1dFBhdGgsIGFwcE5hbWUpLAogICAgKTsKICB9Cn0K",
      "type": "text"
    },
    {
      "path": "commands/melos_command.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmlvJzsKCmltcG9ydCAncGFja2FnZTpjbGlfY29yZS9jbGlfY29yZS5kYXJ0JyBzaG93IEJhc2VNZWxvc0NvbW1hbmQ7CmltcG9ydCAncGFja2FnZTptYXNvbi9tYXNvbi5kYXJ0JzsKCmZpbmFsIGNsYXNzIE1lbG9zQ29tbWFuZCBleHRlbmRzIEJhc2VNZWxvc0NvbW1hbmQgewogIEBvdmVycmlkZQogIEZ1dHVyZTx2b2lkPiBydW4oSG9va0NvbnRleHQgY29udGV4dCkgYXN5bmMgewogICAgZmluYWwgU3RyaW5nIG5hbWUgPSBjb250ZXh0LnZhcnNbJ25hbWUnXTsKICAgIGZpbmFsIGFwcE5hbWUgPSBuYW1lLnNuYWtlQ2FzZTsKCiAgICBhd2FpdCB0cmFja09wZXJhdGlvbigKICAgICAgY29udGV4dCwKICAgICAgc3RhcnRNZXNzYWdlOiAnQWN0aXZhdGluZyBNZWxvcyBnbG9iYWxseScsCiAgICAgIGVuZE1lc3NhZ2U6ICdNZWxvcyBhY3RpdmF0ZWQgZ2xvYmFsbHknLAogICAgICBvcGVyYXRpb246ICgpID0+IFByb2Nlc3MucnVuKAogICAgICAgICdkYXJ0JywKICAgICAgICBbJ3B1YicsICdnbG9iYWwnLCAnYWN0aXZhdGUnLCAnbWVsb3MnXSwKICAgICAgICBydW5JblNoZWxsOiB0cnVlLAogICAgICApLAogICAgKTsKCiAgICBhd2FpdCBib290c3RyYXAoCiAgICAgIGNvbnRleHQ6IGNvbnRleHQsCiAgICAgIHdvcmtzcGFjZVBhdGg6IGFwcE5hbWUsCiAgICApOwogIH0KfQo=",
      "type": "text"
    },
    {
      "path": "post_gen.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmFzeW5jJzsKCmltcG9ydCAncGFja2FnZTptYXNvbi9tYXNvbi5kYXJ0JzsKCmltcG9ydCAnY29tbWFuZHMvZmx1dHRlcl9jcmVhdGVfY29tbWFuZC5kYXJ0JzsKaW1wb3J0ICdjb21tYW5kcy9tZWxvc19jb21tYW5kLmRhcnQnOwoKRnV0dXJlPHZvaWQ+IHJ1bihIb29rQ29udGV4dCBjb250ZXh0KSBhc3luYyB7CiAgZmluYWwgY29tbWFuZHMgPSBbCiAgICBGbHV0dGVyQ3JlYXRlQ29tbWFuZCgpLAogICAgTWVsb3NDb21tYW5kKCksCiAgXTsKCiAgZm9yIChmaW5hbCBjb21tYW5kIGluIGNvbW1hbmRzKSB7CiAgICBhd2FpdCBjb21tYW5kLnJ1bihjb250ZXh0KTsKICB9Cn0K",
      "type": "text"
    },
    {
      "path": "pre_gen.dart",
      "data":
          "aW1wb3J0ICdkYXJ0OmFzeW5jJzsKCmltcG9ydCAncGFja2FnZTpjbGlfY29yZS9jbGlfY29yZS5kYXJ0JzsKaW1wb3J0ICdwYWNrYWdlOm1hc29uL21hc29uLmRhcnQnOwoKRnV0dXJlPHZvaWQ+IHJ1bihIb29rQ29udGV4dCBjb250ZXh0KSBhc3luYyB7CiAgLy8gQ2hlY2sgaWYgd2UncmUgaW4gYSBtb25vIHJlcG8gdXNpbmcgdGhlIHNoYXJlZCB1dGlsaXR5CiAgY29udGV4dC52YXJzWydpc19tb25vX3JlcG8nXSA9IGF3YWl0IEZpbGVVdGlscy5pc01vbm9SZXBvKCk7Cn0K",
      "type": "text"
    },
    {
      "path": "pubspec.yaml",
      "data":
          "bmFtZTogZmx1dHRlcl9wcm9qZWN0X3dpdGhfbW9ub19yZXBvX2hvb2tzCgplbnZpcm9ubWVudDoKICBzZGs6ICc+PTMuMC4wIDw0LjAuMCcKCmRlcGVuZGVuY2llczoKICBtYXNvbjogXjAuMS4wLWRldi41OQogIG1hc29uX2xvZ2dlcjogXjAuMy4xCiAgY2xpX2NvcmU6IF4wLjAuMi1kZXYuNAo=",
      "type": "text"
    }
  ],
  "name": "flutter_project_with_mono_repo",
  "description": "A Flutter project within a Melos-managed mono-repo.",
  "version": "1.0.0",
  "environment": {"mason": ">=0.1.0-dev.52 <0.1.0"},
  "repository":
      "https://github.com/nonstopio/flutter_forge/tree/main/plugins/nonstop_cli",
  "readme": {
    "path": "README.md",
    "data": "IyBmbHV0dGVyX3Byb2plY3Rfd2l0aF9tb25vX3JlcG8=",
    "type": "text"
  },
  "changelog": {
    "path": "CHANGELOG.md",
    "data":
        "IyAxLjAuMAoKLSBJbml0aWFsIHJlbGVhc2UgZm9yIGJ1aWxkaW5nIGEgRmx1dHRlciBBcHAgd2l0aCBNZWxvcwo=",
    "type": "text"
  },
  "license": {
    "path": "LICENSE",
    "data":
        "TUlUIExpY2Vuc2UKCkNvcHlyaWdodCAoYykgMjAyNSBOb25TdG9wIGlvIFRlY2hub2xvZ2llcyBQdnQuIEx0ZC4KClBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHkgb2YKdGhpcyBzb2Z0d2FyZSBhbmQgYXNzb2NpYXRlZCBkb2N1bWVudGF0aW9uIGZpbGVzICh0aGUgIlNvZnR3YXJlIiksIHRvIGRlYWwgaW4KdGhlIFNvZnR3YXJlIHdpdGhvdXQgcmVzdHJpY3Rpb24sIGluY2x1ZGluZyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0cyB0bwp1c2UsIGNvcHksIG1vZGlmeSwgbWVyZ2UsIHB1Ymxpc2gsIGRpc3RyaWJ1dGUsIHN1YmxpY2Vuc2UsIGFuZC9vciBzZWxsIGNvcGllcyBvZgp0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpcyBmdXJuaXNoZWQgdG8gZG8gc28sCnN1YmplY3QgdG8gdGhlIGZvbGxvd2luZyBjb25kaXRpb25zOgoKVGhlIGFib3ZlIGNvcHlyaWdodCBub3RpY2UgYW5kIHRoaXMgcGVybWlzc2lvbiBub3RpY2Ugc2hhbGwgYmUgaW5jbHVkZWQgaW4gYWxsCmNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuCgpUSEUgU09GVFdBUkUgSVMgUFJPVklERUQgIkFTIElTIiwgV0lUSE9VVCBXQVJSQU5UWSBPRiBBTlkgS0lORCwgRVhQUkVTUyBPUgpJTVBMSUVELCBJTkNMVURJTkcgQlVUIE5PVCBMSU1JVEVEIFRPIFRIRSBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWSwgRklUTkVTUwpGT1IgQSBQQVJUSUNVTEFSIFBVUlBPU0UgQU5EIE5PTklORlJJTkdFTUVOVC4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUlMgT1IKQ09QWVJJR0hUIEhPTERFUlMgQkUgTElBQkxFIEZPUiBBTlkgQ0xBSU0sIERBTUFHRVMgT1IgT1RIRVIgTElBQklMSVRZLCBXSEVUSEVSCklOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgVE9SVCBPUiBPVEhFUldJU0UsIEFSSVNJTkcgRlJPTSwgT1VUIE9GIE9SIElOCkNPTk5FQ1RJT04gV0lUSCBUSEUgU09GVFdBUkUgT1IgVEhFIFVTRSBPUiBPVEhFUiBERUFMSU5HUyBJTiBUSEUgU09GVFdBUkUuCg==",
    "type": "text"
  },
  "vars": {
    "name": {
      "type": "string",
      "description": "Name of the project",
      "prompt": "What is your project's name?"
    },
    "description": {
      "type": "string",
      "description": "Description of the project",
      "prompt": "What is your project's description?"
    }
  }
});
