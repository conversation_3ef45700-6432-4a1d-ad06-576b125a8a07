name: 🔍 Commit Message Linter
on: [ pull_request ]

permissions:
  contents: read        # 📖 Required for repository access
  pull-requests: read   # 👁️ Required for PR inspection

jobs:
  lint_commit_messages: # 🕵️ Enforce commit message conventions
    runs-on: ubuntu-latest
    steps:
      - name: 🛸 Checkout Repository
        uses: actions/checkout@v4

      - name: 🎯 Validate Commit Messages
        uses: wagoid/commitlint-github-action@v6
        # 📝 Ensures commit messages follow conventional commit standards