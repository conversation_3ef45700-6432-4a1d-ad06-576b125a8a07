# CLI Core Examples

This directory contains examples demonstrating how to use the various utilities provided by the `cli_core` package.

## What's Demonstrated

1. **Flutter Commands**
   - Creating new Flutter projects
   - Managing project files
   - Running Flutter commands

2. **Melos Commands**
   - Managing dependencies with bootstrap
   - Cleaning workspaces
   - Mono-repo operations

3. **File Utilities**
   - Directory creation and management
   - File operations (read, write, copy, delete)
   - YAML file handling
   - Mono-repo detection

## Example Output

```
// File Utility Examples
Directory created: output/logs
Config content: name: example
version: 1.0.0
description: An example configuration
Is mono-repo? true

// Flutter Command Examples
Creating Flutter project: my_app
Flutter project created successfully
Analysis options removed

// Melos Command Examples
Running melos bootstrap
Dependencies installed successfully
```