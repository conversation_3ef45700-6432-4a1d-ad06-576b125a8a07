name: ns_utils
description: ns_utils is a powerful Flutter utility library that simplifies and enhances your Flutter app development experience. It provides a collection of methods and extensions to streamline your code, making it more readable and efficient. Whether you need responsive design, date and time handling, map operations, string manipulation, or widget customization, ns_utils has got you covered.
version: 1.2.1
homepage: https://github.com/nonstopio/flutter_forge/tree/main/packages/ns_utils
documentation: https://github.com/nonstopio/flutter_forge/tree/main/packages/ns_utils
repository: https://github.com/nonstopio/flutter_forge/tree/main/packages/ns_utils
issue_tracker: https://github.com/nonstopio/flutter_forge/issues

environment:
  sdk: '>=2.18.4 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  after_layout: ^1.2.0
  cached_network_image: ^3.3.0
  device_info_plus: ^9.0.3
  flutter_screenutil: ^5.9.0
  flutter_spinkit: ^5.2.0
  intl: ^0.18.1
  package_info_plus: ^4.1.0
  recase: ^4.1.0
  shared_preferences: ^2.2.1
  visibility_detector: ^0.4.0+2
  objectid: ^3.0.0


  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.3

