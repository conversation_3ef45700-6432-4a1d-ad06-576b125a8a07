name: flutter_forge_workspace

packages:
  - plugins/*
  - plugins/*/*
  - packages/*
  - packages/*/*
  - packages/nonstop_cli/bricks/flutter_app_for_mono_repo/hooks
  - packages/nonstop_cli/bricks/flutter_package_for_mono_repo/hooks
  - packages/nonstop_cli/bricks/flutter_project_with_mono_repo/hooks
  - packages/nonstop_cli/bricks/flutter_plugin_for_mono_repo/hooks

command:
  version:
    workspaceChangelog: false
    fetchTags: true
    updateGitTagRefs: false
    linkToCommits: false
    hooks:
      preCommit: melos run update_nonstop_cli_version && melos run update_nonstop_cli_bundles

scripts:
  lint:
    description: "Run format and analyze all packages"
    run: |
      dart format --set-exit-if-changed . &&
      dart analyze --fatal-infos .
    exec:
      concurrency: 5
      failFast: true
  update_nonstop_cli_version:
    run: dart run tools/update_nonstop_cli_version.dart
  update_nonstop_cli_bundles:
    run: dart run tools/update_nonstop_cli_bundles.dart

