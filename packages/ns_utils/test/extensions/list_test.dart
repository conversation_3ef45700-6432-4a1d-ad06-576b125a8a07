import 'package:flutter_test/flutter_test.dart';
import 'package:ns_utils/src.dart';

void main() {
  group('ListExtensions', () {
    test('toJson', () {
      expect([12, 12].to<PERSON><PERSON>(), '[12,12]');
      expect([].to<PERSON><PERSON>(), '[]');
      expect(['123', '1234'].to<PERSON>son(), '["123","1234"]');
    });

    test('containWithoutCase', () {
      List<String> list = ['Test', 'User'];
      expect(list.containWithoutCase('test'), true);
      expect(list.containWithoutCase('User1'), false);
    });
  });
}
