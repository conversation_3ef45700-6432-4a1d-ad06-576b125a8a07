name: nonstop_cli
description: A command-line interface for the Flutter Framework to generate Flutter projects, features and schemas.
version: 0.0.7
homepage: https://github.com/nonstopio/flutter_forge
documentation: https://github.com/nonstopio/flutter_forge/tree/main/packages/nonstop_cli
repository: https://github.com/nonstopio/flutter_forge/tree/main/packages/nonstop_cli
issue_tracker: https://github.com/nonstopio/flutter_forge/issues

topics:
  - melos
  - codegen
  - template
  - cli

screenshots:
  - description: The Nonstop CLI in action showing the creation of a new Flutter project.
    path: _images/cli.png

environment:
  sdk: ^3.0.0

dependencies:
  args: ^2.5.0
  cli_completion: ^0.5.1
  cli_core: ^0.0.2-dev.4
  mason: ^0.1.0
  mason_logger: ^0.3.1
  meta: ^1.15.0
  path: ^1.9.0
  process_run: ^1.2.1+1
  pub_updater: ^0.5.0
  universal_io: ^2.2.2
  yaml_edit: ^2.2.1

dev_dependencies:
  build_runner: ^2.4.12
  build_verify: ^3.1.0
  build_version: ^2.1.1
  flutter_lints: ^5.0.0
  mocktail: ^1.0.4
  test: ^1.25.8

executables:
  nonstop:
  ns:
  nsio:

