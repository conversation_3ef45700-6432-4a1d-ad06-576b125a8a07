import 'package:nonstop_cli/commands/doctor/src/utils/utils.dart';

class MelosValidator extends DoctorValida<PERSON> {
  MelosValidator() : super('<PERSON>os');

  @override
  String get installHelp =>
      'https://melos.invertase.dev/~melos-latest/getting-started';

  @override
  Future<ValidationResult> validate() => commandVersionValidator(
        this,
        command: 'melos',
      );

  @override
  String extractVersion(String output) {
    final versionRegex = RegExp(r'^(\d+\.\d+\.\d+)');
    final match = versionRegex.firstMatch(output);
    return match?.group(1) ?? 'Unknown';
  }
}
