name: timer_button
description: Timer But<PERSON> is a Flutter package that offers a customizable button widget capable of activation after a designated time interval.
version: 2.3.1
homepage: https://github.com/nonstopio/flutter_forge/tree/main/packages/timer_button
documentation: https://github.com/nonstopio/flutter_forge/tree/main/packages/timer_button
repository: https://github.com/nonstopio/flutter_forge/tree/main/packages/timer_button
issue_tracker: https://github.com/nonstopio/flutter_forge/issues
screenshots:
  - description: Demo But<PERSON>
    path: screenshots/demo.png

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.3
