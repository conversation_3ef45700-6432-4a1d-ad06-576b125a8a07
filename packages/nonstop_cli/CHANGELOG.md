## 0.0.7

 - **FEAT**(nonstop_cli): add executables ‘ns’ and ‘nsio’ as aliases for nonstop.
 - **FEAT**(nonstop_cli): Add executables ‘ns’ and ‘nsio’ as aliases for nonstop.
 - **DOCS**: add section for CLI aliases in README.
 - **DOCS**: add section for CLI aliases in README.

## 0.0.6

 - **FEAT**(nonstop_cli): Add executables ‘ns’ and ‘nsio’ as aliases for nonstop.
 - **DOCS**: add section for CLI aliases in README.

## 0.0.5+2

 - Update a dependency to the latest release.

## 0.0.5+1

 - **FIX**: remove the cli_core path and use the version instead.

## 0.0.5

 - **REFACTOR**(cli_core): rename cli_utils to cli_core.
 - **FEAT**(cli_utils): introduce cli_utils package with shared utilities for CLI operations.
 - **DOCS**: update README and CHANGELOG for cli_core branding and features.

## 0.0.4

 - **FEAT**(nonstop_cli): add Flutter plugin support for mono-repo with initial templates and hooks.
 - **DOCS**(nonstop_cli): update README and add INSTALL.md for improved installation guidance.

## 0.0.3

 - **FEAT**(flutter_project_with_mono_repo): added monorepo configuration.
 - **FEAT**(nonstop_cli): add flutter_app_for_mono_repo template.
 - **FEAT**(nonstop_cli): add custom logging and signature extensions.
 - **DOCS**(command): update help text for mono-repo generation options.
 - **DOCS**(nonstop_cli): enhance README with detailed CLI usage and examples; update .gitignore.

## 0.0.2

 - **FEAT**(nonstop_cli): improve CLI documentation and project creation options.
 - **FEAT**(nonstop_cli): enhance Flutter package template for mono-repos.
 - **FEAT**(nonstop_cli): add support for creating Flutter packages and projects in mono-repos.
 - **DOCS**(nonstop_cli): update documentation links and image references.

## 0.0.1+3

 - **FIX**(readme): update command usage example in README.md.

## 0.0.1+2

 - **FIX**(command): update invocation format in command.dart and README.

## 0.0.1+1

 - **FIX**(pubspec): remove redundant topics from pubspec.yaml.
 - **FIX**(nonstop_cli): rename nonstop_cli.dart to nonstop.dart.

## 0.0.1

 - **FIX**(nonstop_cli): rename nonstop_cli.dart to nonstop.dart.
 - **FEAT**(nonstop_cli): initial release of core commands.

