name: ns_firebase_utils
description: This package provides a comprehensive set of methods and extensions for Firebase, simplifying its integration into your Flutter projects while also adding valuable functionality.
version: 1.2.1
homepage: https://github.com/nonstopio/flutter_forge/tree/main/packages/ns_firebase_utils
documentation: https://github.com/nonstopio/flutter_forge/tree/main/packages/ns_firebase_utils
repository: https://github.com/nonstopio/flutter_forge/tree/main/packages/ns_firebase_utils
issue_tracker: https://github.com/nonstopio/flutter_forge/issues

environment:
  sdk: '>=2.17.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cloud_firestore: ^5.5.0
  cloud_functions: ^5.1.5
  fake_cloud_firestore: ^3.1.0
  firebase_analytics: ^11.3.5
  firebase_auth: ^5.3.3
  firebase_core: ^3.8.0
  firebase_crashlytics: ^4.1.5
  firebase_dynamic_links: ^6.0.10
  firebase_messaging: ^15.1.5
  firebase_remote_config: ^5.1.5
  firebase_storage: ^12.3.6

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
