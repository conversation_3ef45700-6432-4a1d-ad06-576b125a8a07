// Country model:
// [
//    Country name,
//    Regions,
//    iso2 code,
//    International dial code,
//    Format (if available),
//    Order priority (if >1 country with same dial code),
//    Area codes (if >1 country with same dial code)
// ]
//
// Regions:
// ['america', 'europe', 'asia', 'oceania', 'africa']
//
// Sub-regions:
// ['north-america', 'south-america', 'central-america', 'carribean',
//  'eu-union', 'ex-ussr', 'ex-yugos', 'baltic', 'middle-east', 'north-africa']
import '../data/models/country.dart';

List<CountryModel> rawCountries = [
  const CountryModel(
    countryName: 'Afghanistan',
    regions: {'asia'},
    iso2Code: 'af',
    intlDialCode: '93',
    flag: '🇦🇫',
  ),
  const CountryModel(
    countryName: 'Albania',
    regions: {'europe'},
    iso2Code: 'al',
    intlDialCode: '355',
    flag: '🇦🇱',
  ),
  const CountryModel(
    countryName: 'Algeria',
    regions: {'africa', 'north-africa'},
    iso2Code: 'dz',
    intlDialCode: '213',
    flag: '🇩🇿',
  ),
  const CountryModel(
    countryName: 'Andorra',
    regions: {'europe'},
    iso2Code: 'ad',
    intlDialCode: '376',
    flag: '🇦🇩',
  ),
  const CountryModel(
    countryName: 'Angola',
    regions: {'africa'},
    iso2Code: 'ao',
    intlDialCode: '244',
    flag: '🇦🇴',
  ),
  const CountryModel(
    countryName: 'Antigua and Barbuda',
    regions: {'america', 'carribean'},
    iso2Code: 'ag',
    intlDialCode: '1268',
    flag: '🇦🇬',
  ),
  const CountryModel(
    countryName: 'Argentina',
    regions: {'america', 'south-america'},
    iso2Code: 'ar',
    intlDialCode: '54',
    flag: '🇦🇷',
    format: '(..) ........',
    orderPriority: 0,
    areaCodes: {
      '11',
      '221',
      '223',
      '261',
      '264',
      '2652',
      '280',
      '2905',
      '291',
      '2920',
      '2966',
      '299',
      '341',
      '342',
      '343',
      '351',
      '376',
      '379',
      '381',
      '3833',
      '385',
      '387',
      '388',
    },
  ),
  const CountryModel(
    countryName: 'Argentina',
    regions: {'america', 'south-america'},
    iso2Code: 'ar',
    intlDialCode: '54',
    flag: '🇦🇷',
    format: '(..) ........',
    orderPriority: 0,
    areaCodes: {
      '11',
      '221',
      '223',
      '261',
      '264',
      '2652',
      '280',
      '2905',
      '291',
      '2920',
      '2966',
      '299',
      '341',
      '342',
      '343',
      '351',
      '376',
      '379',
      '381',
      '3833',
      '385',
      '387',
      '388',
    },
  ),
  const CountryModel(
    countryName: 'Armenia',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'am',
    intlDialCode: '374',
    flag: '🇦🇲',
    format: '.. ......',
  ),
  const CountryModel(
    countryName: 'Aruba',
    regions: {'america', 'carribean'},
    iso2Code: 'aw',
    intlDialCode: '297',
    flag: '🇦🇼',
  ),
  const CountryModel(
    countryName: 'Australia',
    regions: {'oceania'},
    iso2Code: 'au',
    intlDialCode: '61',
    flag: '🇦🇺',
    format: '(..) .... ....',
    orderPriority: 0,
    areaCodes: {'2', '3', '4', '7', '8', '02', '03', '04', '07', '08'},
  ),
  const CountryModel(
    countryName: 'Austria',
    regions: {'europe', 'eu-union'},
    iso2Code: 'at',
    intlDialCode: '43',
    flag: '🇦🇹',
  ),
  const CountryModel(
    countryName: 'Azerbaijan',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'az',
    intlDialCode: '994',
    flag: '🇦🇿',
    format: '(..) ... .. ..',
  ),
  const CountryModel(
    countryName: 'Bahamas',
    regions: {'america', 'carribean'},
    iso2Code: 'bs',
    intlDialCode: '1242',
    flag: '🇧🇸',
  ),
  const CountryModel(
    countryName: 'Bahrain',
    regions: {'middle-east'},
    iso2Code: 'bh',
    intlDialCode: '973',
    flag: '🇧🇭',
  ),
  const CountryModel(
    countryName: 'Bangladesh',
    regions: {'asia'},
    iso2Code: 'bd',
    intlDialCode: '880',
    flag: '🇧🇩',
  ),
  const CountryModel(
    countryName: 'Barbados',
    regions: {'america', 'carribean'},
    iso2Code: 'bb',
    intlDialCode: '1246',
    flag: '🇧🇧',
  ),
  const CountryModel(
    countryName: 'Belarus',
    regions: {'europe', 'ex-ussr'},
    iso2Code: 'by',
    intlDialCode: '375',
    flag: '🇧🇾',
    format: '(..) ... .. ..',
  ),
  const CountryModel(
    countryName: 'Belgium',
    regions: {'europe', 'eu-union'},
    iso2Code: 'be',
    intlDialCode: '32',
    flag: '🇧🇪',
    format: '... .. .. ..',
  ),
  const CountryModel(
    countryName: 'Belize',
    regions: {'america', 'central-america'},
    iso2Code: 'bz',
    intlDialCode: '501',
    flag: '🇧🇿',
  ),
  const CountryModel(
    countryName: 'Benin',
    regions: {'africa'},
    iso2Code: 'bj',
    intlDialCode: '229',
    flag: '🇧🇯',
  ),
  const CountryModel(
    countryName: 'Bhutan',
    regions: {'asia'},
    iso2Code: 'bt',
    intlDialCode: '975',
    flag: '🇧🇹',
  ),
  const CountryModel(
    countryName: 'Bolivia',
    regions: {'america', 'south-america'},
    iso2Code: 'bo',
    intlDialCode: '591',
    flag: '🇧🇴',
  ),
  const CountryModel(
    countryName: 'Bosnia and Herzegovina',
    regions: {'europe', 'ex-yugos'},
    iso2Code: 'ba',
    intlDialCode: '387',
    flag: '🇧🇦',
  ),
  const CountryModel(
    countryName: 'Botswana',
    regions: {'africa'},
    iso2Code: 'bw',
    intlDialCode: '267',
    flag: '🇧🇼',
  ),
  const CountryModel(
    countryName: 'Brazil',
    regions: {'america', 'south-america'},
    iso2Code: 'br',
    intlDialCode: '55',
    flag: '🇧🇷',
    format: '(..) .........',
  ),
  const CountryModel(
    countryName: 'British Indian Ocean Territory',
    regions: {'asia'},
    iso2Code: 'io',
    intlDialCode: '246',
    flag: '🇮🇴',
  ),
  const CountryModel(
    countryName: 'Brunei',
    regions: {'asia'},
    iso2Code: 'bn',
    intlDialCode: '673',
    flag: '🇧🇳',
  ),
  const CountryModel(
    countryName: 'Bulgaria',
    regions: {'europe', 'eu-union'},
    iso2Code: 'bg',
    intlDialCode: '359',
    flag: '🇧🇬',
  ),
  const CountryModel(
    countryName: 'Burkina Faso',
    regions: {'africa'},
    iso2Code: 'bf',
    intlDialCode: '226',
    flag: '🇧🇫',
  ),
  const CountryModel(
    countryName: 'Burundi',
    regions: {'africa'},
    iso2Code: 'bi',
    intlDialCode: '257',
    flag: '🇧🇮',
  ),
  const CountryModel(
    countryName: 'Cambodia',
    regions: {'asia'},
    iso2Code: 'kh',
    intlDialCode: '855',
    flag: '🇰🇭',
  ),
  const CountryModel(
    countryName: 'Cameroon',
    regions: {'africa'},
    iso2Code: 'cm',
    intlDialCode: '237',
    flag: '🇨🇲',
  ),
  const CountryModel(
    countryName: 'Canada',
    regions: {'america', 'north-america'},
    iso2Code: 'ca',
    intlDialCode: '1',
    flag: '🇨🇦',
    format: '(...) ...-....',
    orderPriority: 1,
    areaCodes: {
      '204',
      '226',
      '236',
      '249',
      '250',
      '289',
      '306',
      '343',
      '365',
      '387',
      '403',
      '416',
      '418',
      '431',
      '437',
      '438',
      '450',
      '506',
      '514',
      '519',
      '548',
      '579',
      '581',
      '587',
      '604',
      '613',
      '639',
      '647',
      '672',
      '705',
      '709',
      '742',
      '778',
      '780',
      '782',
      '807',
      '819',
      '825',
      '867',
      '873',
      '902',
      '905',
    },
  ),
  const CountryModel(
    countryName: 'Cape Verde',
    regions: {'africa'},
    iso2Code: 'cv',
    intlDialCode: '238',
    flag: '🇨🇻',
  ),
  const CountryModel(
    countryName: 'Caribbean Netherlands',
    regions: {'america', 'carribean'},
    iso2Code: 'bq',
    intlDialCode: '599',
    flag: '🇧🇶',
    orderPriority: 1,
  ),
  const CountryModel(
    countryName: 'Central African Republic',
    regions: {'africa'},
    iso2Code: 'cf',
    intlDialCode: '236',
    flag: '🇨🇫',
  ),
  const CountryModel(
    countryName: 'Chad',
    regions: {'africa'},
    iso2Code: 'td',
    intlDialCode: '235',
    flag: '🇹🇩',
  ),
  const CountryModel(
    countryName: 'Chile',
    regions: {'america', 'south-america'},
    iso2Code: 'cl',
    intlDialCode: '56',
    flag: '🇨🇱',
  ),
  const CountryModel(
    countryName: 'China',
    regions: {'asia'},
    iso2Code: 'cn',
    intlDialCode: '86',
    flag: '🇨🇳',
    format: '..-.........',
  ),
  const CountryModel(
    countryName: 'Colombia',
    regions: {'america', 'south-america'},
    iso2Code: 'co',
    intlDialCode: '57',
    flag: '🇨🇴',
    format: '... ... ....',
  ),
  const CountryModel(
    countryName: 'Comoros',
    regions: {'africa'},
    iso2Code: 'km',
    intlDialCode: '269',
    flag: '🇰🇲',
  ),
  const CountryModel(
    countryName: 'Congo',
    regions: {'africa'},
    iso2Code: 'cd',
    intlDialCode: '243',
    flag: '🇨🇩',
  ),
  const CountryModel(
    countryName: 'Congo',
    regions: {'africa'},
    iso2Code: 'cg',
    intlDialCode: '242',
    flag: '🇨🇬',
  ),
  const CountryModel(
    countryName: 'Costa Rica',
    regions: {'america', 'central-america'},
    iso2Code: 'cr',
    intlDialCode: '506',
    flag: '🇨🇷',
    format: '....-....',
  ),
  const CountryModel(
    countryName: 'Croatia',
    regions: {'europe', 'eu-union', 'ex-yugos'},
    iso2Code: 'hr',
    intlDialCode: '385',
    flag: '🇭🇷',
  ),
  const CountryModel(
    countryName: 'Cuba',
    regions: {'america', 'carribean'},
    iso2Code: 'cu',
    intlDialCode: '53',
    flag: '🇨🇺',
  ),
  const CountryModel(
    countryName: 'Curaçao',
    regions: {'america', 'carribean'},
    iso2Code: 'cw',
    intlDialCode: '599',
    flag: '🇨🇼',
    orderPriority: 0,
  ),
  const CountryModel(
    countryName: 'Cyprus',
    regions: {'europe', 'eu-union'},
    iso2Code: 'cy',
    intlDialCode: '357',
    flag: '🇨🇾',
    format: '.. ......',
  ),
  const CountryModel(
    countryName: 'Czech Republic',
    regions: {'europe', 'eu-union'},
    iso2Code: 'cz',
    intlDialCode: '420',
    flag: '🇨🇿',
    format: '... ... ...',
  ),
  const CountryModel(
    countryName: 'Côte d’Ivoire',
    regions: {'africa'},
    iso2Code: 'ci',
    intlDialCode: '225',
    flag: '🇨🇮',
    format: '.. .. .. ..',
  ),
  const CountryModel(
    countryName: 'Denmark',
    regions: {'europe', 'eu-union', 'baltic'},
    iso2Code: 'dk',
    intlDialCode: '45',
    flag: '🇩🇰',
    format: '.. .. .. ..',
  ),
  const CountryModel(
    countryName: 'Djibouti',
    regions: {'africa'},
    iso2Code: 'dj',
    intlDialCode: '253',
    flag: '🇩🇯',
  ),
  const CountryModel(
    countryName: 'Dominica',
    regions: {'america', 'carribean'},
    iso2Code: 'dm',
    intlDialCode: '1767',
    flag: '🇩🇲',
  ),
  const CountryModel(
    countryName: 'Dominican Republic',
    regions: {'america', 'carribean'},
    iso2Code: 'do',
    intlDialCode: '1',
    flag: '🇩🇴',
    orderPriority: 2,
    areaCodes: {'809', '829', '849'},
  ),
  const CountryModel(
    countryName: 'Ecuador',
    regions: {'america', 'south-america'},
    iso2Code: 'ec',
    intlDialCode: '593',
    flag: '🇪🇨',
  ),
  const CountryModel(
    countryName: 'Egypt',
    regions: {'africa', 'north-africa'},
    iso2Code: 'eg',
    intlDialCode: '20',
    flag: '🇪🇬',
  ),
  const CountryModel(
    countryName: 'El Salvador',
    regions: {'america', 'central-america'},
    iso2Code: 'sv',
    intlDialCode: '503',
    flag: '🇸🇻',
    format: '....-....',
  ),
  const CountryModel(
    countryName: 'Equatorial Guinea',
    regions: {'africa'},
    iso2Code: 'gq',
    intlDialCode: '240',
    flag: '🇬🇶',
  ),
  const CountryModel(
    countryName: 'Eritrea',
    regions: {'africa'},
    iso2Code: 'er',
    intlDialCode: '291',
    flag: '🇪🇷',
  ),
  const CountryModel(
    countryName: 'Estonia',
    regions: {'europe', 'eu-union', 'ex-ussr', 'baltic'},
    iso2Code: 'ee',
    intlDialCode: '372',
    flag: '🇪🇪',
    format: '.... ......',
  ),
  const CountryModel(
    countryName: 'Ethiopia',
    regions: {'africa'},
    iso2Code: 'et',
    intlDialCode: '251',
    flag: '🇪🇹',
  ),
  const CountryModel(
    countryName: 'Fiji',
    regions: {'oceania'},
    iso2Code: 'fj',
    intlDialCode: '679',
    flag: '🇫🇯',
  ),
  const CountryModel(
    countryName: 'Finland',
    regions: {'europe', 'eu-union', 'baltic'},
    iso2Code: 'fi',
    intlDialCode: '358',
    flag: '🇫🇮',
    format: '.. ... .. ..',
  ),
  const CountryModel(
    countryName: 'France',
    regions: {'europe', 'eu-union'},
    iso2Code: 'fr',
    intlDialCode: '33',
    flag: '🇫🇷',
    format: '. .. .. .. ..',
  ),
  const CountryModel(
    countryName: 'French Guiana',
    regions: {'america', 'south-america'},
    iso2Code: 'gf',
    intlDialCode: '594',
    flag: '🇬🇫',
  ),
  const CountryModel(
    countryName: 'French Polynesia',
    regions: {'oceania'},
    iso2Code: 'pf',
    intlDialCode: '689',
    flag: '🇵🇫',
  ),
  const CountryModel(
    countryName: 'Gabon',
    regions: {'africa'},
    iso2Code: 'ga',
    intlDialCode: '241',
    flag: '🇬🇦',
  ),
  const CountryModel(
    countryName: 'Gambia',
    regions: {'africa'},
    iso2Code: 'gm',
    intlDialCode: '220',
    flag: '🇬🇲',
  ),
  const CountryModel(
    countryName: 'Georgia',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'ge',
    intlDialCode: '995',
    flag: '🇬🇪',
  ),
  const CountryModel(
    countryName: 'Germany',
    regions: {'europe', 'eu-union', 'baltic'},
    iso2Code: 'de',
    intlDialCode: '49',
    flag: '🇩🇪',
    format: '.... ........',
  ),
  const CountryModel(
    countryName: 'Ghana',
    regions: {'africa'},
    iso2Code: 'gh',
    intlDialCode: '233',
    flag: '🇬🇭',
  ),
  const CountryModel(
    countryName: 'Greece',
    regions: {'europe', 'eu-union'},
    iso2Code: 'gr',
    intlDialCode: '30',
    flag: '🇬🇷',
  ),
  const CountryModel(
    countryName: 'Grenada',
    regions: {'america', 'carribean'},
    iso2Code: 'gd',
    intlDialCode: '1473',
    flag: '🇬🇩',
  ),
  const CountryModel(
    countryName: 'Guadeloupe',
    regions: {'america', 'carribean'},
    iso2Code: 'gp',
    intlDialCode: '590',
    flag: '🇬🇵',
    orderPriority: 0,
  ),
  const CountryModel(
    countryName: 'Guam',
    regions: {'oceania'},
    iso2Code: 'gu',
    intlDialCode: '1671',
    flag: '🇬🇺',
  ),
  const CountryModel(
    countryName: 'Guatemala',
    regions: {'america', 'central-america'},
    iso2Code: 'gt',
    intlDialCode: '502',
    flag: '🇬🇹',
    format: '....-....',
  ),
  const CountryModel(
    countryName: 'Guinea',
    regions: {'africa'},
    iso2Code: 'gn',
    intlDialCode: '224',
    flag: '🇬🇳',
  ),
  const CountryModel(
    countryName: 'Guinea-Bissau',
    regions: {'africa'},
    iso2Code: 'gw',
    intlDialCode: '245',
    flag: '🇬🇼',
  ),
  const CountryModel(
    countryName: 'Guyana',
    regions: {'america', 'south-america'},
    iso2Code: 'gy',
    intlDialCode: '592',
    flag: '🇬🇾',
  ),
  const CountryModel(
    countryName: 'Haiti',
    regions: {'america', 'carribean'},
    iso2Code: 'ht',
    intlDialCode: '509',
    flag: '🇭🇹',
    format: '....-....',
  ),
  const CountryModel(
    countryName: 'Honduras',
    regions: {'america', 'central-america'},
    iso2Code: 'hn',
    intlDialCode: '504',
    flag: '🇭🇳',
  ),
  const CountryModel(
    countryName: 'Hong Kong',
    regions: {'asia'},
    iso2Code: 'hk',
    intlDialCode: '852',
    flag: '🇭🇰',
    format: '.... ....',
  ),
  const CountryModel(
    countryName: 'Hungary',
    regions: {'europe', 'eu-union'},
    iso2Code: 'hu',
    intlDialCode: '36',
    flag: '🇭🇺',
  ),
  const CountryModel(
    countryName: 'Iceland',
    regions: {'europe'},
    iso2Code: 'is',
    intlDialCode: '354',
    flag: '🇮🇸',
    format: '... ....',
  ),
  const CountryModel(
    countryName: 'India',
    regions: {'asia'},
    iso2Code: 'in',
    intlDialCode: '91',
    flag: '🇮🇳',
    format: '.....-.....',
  ),
  const CountryModel(
    countryName: 'Indonesia',
    regions: {'asia'},
    iso2Code: 'id',
    intlDialCode: '62',
    flag: '🇮🇩',
  ),
  const CountryModel(
    countryName: 'Iran',
    regions: {'middle-east'},
    iso2Code: 'ir',
    intlDialCode: '98',
    flag: '🇮🇷',
    format: '... ... ....',
  ),
  const CountryModel(
    countryName: 'Iraq',
    regions: {'middle-east'},
    iso2Code: 'iq',
    intlDialCode: '964',
    flag: '🇮🇶',
  ),
  const CountryModel(
    countryName: 'Ireland',
    regions: {'europe', 'eu-union'},
    iso2Code: 'ie',
    intlDialCode: '353',
    flag: '🇮🇪',
    format: '.. .......',
  ),
  const CountryModel(
    countryName: 'Israel',
    regions: {'middle-east'},
    iso2Code: 'il',
    intlDialCode: '972',
    flag: '🇮🇱',
    format: '... ... ....',
  ),
  const CountryModel(
    countryName: 'Italy',
    regions: {'europe', 'eu-union'},
    iso2Code: 'it',
    intlDialCode: '39',
    flag: '🇮🇹',
    format: '... .......',
    orderPriority: 0,
  ),
  const CountryModel(
    countryName: 'Jamaica',
    regions: {'america', 'carribean'},
    iso2Code: 'jm',
    intlDialCode: '1876',
    flag: '🇯🇲',
  ),
  const CountryModel(
    countryName: 'Japan',
    regions: {'asia'},
    iso2Code: 'jp',
    intlDialCode: '81',
    flag: '🇯🇵',
    format: '.. .... ....',
  ),
  const CountryModel(
    countryName: 'Jordan',
    regions: {'middle-east'},
    iso2Code: 'jo',
    intlDialCode: '962',
    flag: '🇯🇴',
  ),
  const CountryModel(
    countryName: 'Kazakhstan',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'kz',
    intlDialCode: '7',
    flag: '🇰🇿',
    format: '... ...-..-..',
    orderPriority: 1,
    areaCodes: {
      '310',
      '311',
      '312',
      '313',
      '315',
      '318',
      '321',
      '324',
      '325',
      '326',
      '327',
      '336',
      '7172',
      '73622',
    },
  ),
  const CountryModel(
    countryName: 'Kenya',
    regions: {'africa'},
    iso2Code: 'ke',
    intlDialCode: '254',
    flag: '🇰🇪',
  ),
  const CountryModel(
    countryName: 'Kiribati',
    regions: {'oceania'},
    iso2Code: 'ki',
    intlDialCode: '686',
    flag: '🇰🇮',
  ),
  const CountryModel(
    countryName: 'Kosovo',
    regions: {'europe', 'ex-yugos'},
    iso2Code: 'xk',
    intlDialCode: '383',
    flag: '🇽🇰',
  ),
  const CountryModel(
    countryName: 'Kuwait',
    regions: {'middle-east'},
    iso2Code: 'kw',
    intlDialCode: '965',
    flag: '🇰🇼',
  ),
  const CountryModel(
    countryName: 'Kyrgyzstan',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'kg',
    intlDialCode: '996',
    flag: '🇰🇬',
    format: '... ... ...',
  ),
  const CountryModel(
    countryName: 'Laos',
    regions: {'asia'},
    iso2Code: 'la',
    intlDialCode: '856',
    flag: '🇱🇦',
  ),
  const CountryModel(
    countryName: 'Latvia',
    regions: {'europe', 'eu-union', 'ex-ussr', 'baltic'},
    iso2Code: 'lv',
    intlDialCode: '371',
    flag: '🇱🇻',
    format: '.. ... ...',
  ),
  const CountryModel(
    countryName: 'Lebanon',
    regions: {'middle-east'},
    iso2Code: 'lb',
    intlDialCode: '961',
    flag: '🇱🇧',
  ),
  const CountryModel(
    countryName: 'Lesotho',
    regions: {'africa'},
    iso2Code: 'ls',
    intlDialCode: '266',
    flag: '🇱🇸',
  ),
  const CountryModel(
    countryName: 'Liberia',
    regions: {'africa'},
    iso2Code: 'lr',
    intlDialCode: '231',
    flag: '🇱🇷',
  ),
  const CountryModel(
    countryName: 'Libya',
    regions: {'africa', 'north-africa'},
    iso2Code: 'ly',
    intlDialCode: '218',
    flag: '🇱🇾',
  ),
  const CountryModel(
    countryName: 'Liechtenstein',
    regions: {'europe'},
    iso2Code: 'li',
    intlDialCode: '423',
    flag: '🇱🇮',
  ),
  const CountryModel(
    countryName: 'Lithuania',
    regions: {'europe', 'eu-union', 'ex-ussr', 'baltic'},
    iso2Code: 'lt',
    intlDialCode: '370',
    flag: '🇱🇹',
  ),
  const CountryModel(
    countryName: 'Luxembourg',
    regions: {'europe', 'eu-union'},
    iso2Code: 'lu',
    intlDialCode: '352',
    flag: '🇱🇺',
  ),
  const CountryModel(
    countryName: 'Macau',
    regions: {'asia'},
    iso2Code: 'mo',
    intlDialCode: '853',
    flag: '🇲🇴',
  ),
  const CountryModel(
    countryName: 'Macedonia',
    regions: {'europe', 'ex-yugos'},
    iso2Code: 'mk',
    intlDialCode: '389',
    flag: '🇲🇰',
  ),
  const CountryModel(
    countryName: 'Madagascar',
    regions: {'africa'},
    iso2Code: 'mg',
    intlDialCode: '261',
    flag: '🇲🇬',
  ),
  const CountryModel(
    countryName: 'Malawi',
    regions: {'africa'},
    iso2Code: 'mw',
    intlDialCode: '265',
    flag: '🇲🇼',
  ),
  const CountryModel(
    countryName: 'Malaysia',
    regions: {'asia'},
    iso2Code: 'my',
    intlDialCode: '60',
    flag: '🇲🇾',
    format: '..-....-....',
  ),
  const CountryModel(
    countryName: 'Maldives',
    regions: {'asia'},
    iso2Code: 'mv',
    intlDialCode: '960',
    flag: '🇲🇻',
  ),
  const CountryModel(
    countryName: 'Mali',
    regions: {'africa'},
    iso2Code: 'ml',
    intlDialCode: '223',
    flag: '🇲🇱',
  ),
  const CountryModel(
    countryName: 'Malta',
    regions: {'europe', 'eu-union'},
    iso2Code: 'mt',
    intlDialCode: '356',
    flag: '🇲🇹',
  ),
  const CountryModel(
    countryName: 'Marshall Islands',
    regions: {'oceania'},
    iso2Code: 'mh',
    intlDialCode: '692',
    flag: '🇲🇭',
  ),
  const CountryModel(
    countryName: 'Martinique',
    regions: {'america', 'carribean'},
    iso2Code: 'mq',
    intlDialCode: '596',
    flag: '🇲🇶',
  ),
  const CountryModel(
    countryName: 'Mauritania',
    regions: {'africa'},
    iso2Code: 'mr',
    intlDialCode: '222',
    flag: '🇲🇷',
  ),
  const CountryModel(
    countryName: 'Mauritius',
    regions: {'africa'},
    iso2Code: 'mu',
    intlDialCode: '230',
    flag: '🇲🇺',
  ),
  const CountryModel(
    countryName: 'Mexico',
    regions: {'america', 'central-america'},
    iso2Code: 'mx',
    intlDialCode: '52',
    flag: '🇲🇽',
    format: '... ... ....',
    orderPriority: 0,
    areaCodes: {'55', '81', '33', '656', '664', '998', '774', '229'},
  ),
  const CountryModel(
    countryName: 'Micronesia',
    regions: {'oceania'},
    iso2Code: 'fm',
    intlDialCode: '691',
    flag: '🇫🇲',
  ),
  const CountryModel(
    countryName: 'Moldova',
    regions: {'europe'},
    iso2Code: 'md',
    intlDialCode: '373',
    flag: '🇲🇩',
    format: '(..) ..-..-..',
  ),
  const CountryModel(
    countryName: 'Monaco',
    regions: {'europe'},
    iso2Code: 'mc',
    intlDialCode: '377',
    flag: '🇲🇨',
  ),
  const CountryModel(
    countryName: 'Mongolia',
    regions: {'asia'},
    iso2Code: 'mn',
    intlDialCode: '976',
    flag: '🇲🇳',
  ),
  const CountryModel(
    countryName: 'Montenegro',
    regions: {'europe', 'ex-yugos'},
    iso2Code: 'me',
    intlDialCode: '382',
    flag: '🇲🇪',
  ),
  const CountryModel(
    countryName: 'Morocco',
    regions: {'africa', 'north-africa'},
    iso2Code: 'ma',
    intlDialCode: '212',
    flag: '🇲🇦',
  ),
  const CountryModel(
    countryName: 'Mozambique',
    regions: {'africa'},
    iso2Code: 'mz',
    intlDialCode: '258',
    flag: '🇲🇿',
  ),
  const CountryModel(
    countryName: 'Myanmar',
    regions: {'asia'},
    iso2Code: 'mm',
    intlDialCode: '95',
    flag: '🇲🇲',
  ),
  const CountryModel(
    countryName: 'Namibia',
    regions: {'africa'},
    iso2Code: 'na',
    intlDialCode: '264',
    flag: '🇳🇦',
  ),
  const CountryModel(
    countryName: 'Nauru',
    regions: {'africa'},
    iso2Code: 'nr',
    intlDialCode: '674',
    flag: '🇳🇷',
  ),
  const CountryModel(
    countryName: 'Nepal',
    regions: {'asia'},
    iso2Code: 'np',
    intlDialCode: '977',
    flag: '🇳🇵',
  ),
  const CountryModel(
    countryName: 'Netherlands',
    regions: {'europe', 'eu-union'},
    iso2Code: 'nl',
    intlDialCode: '31',
    flag: '🇳🇱',
    format: '.. ........',
  ),
  const CountryModel(
    countryName: 'New Caledonia',
    regions: {'oceania'},
    iso2Code: 'nc',
    intlDialCode: '687',
    flag: '🇳🇨',
  ),
  const CountryModel(
    countryName: 'New Zealand',
    regions: {'oceania'},
    iso2Code: 'nz',
    intlDialCode: '64',
    flag: '🇳🇿',
    format: '...-...-....',
  ),
  const CountryModel(
    countryName: 'Nicaragua',
    regions: {'america', 'central-america'},
    iso2Code: 'ni',
    intlDialCode: '505',
    flag: '🇳🇮',
  ),
  const CountryModel(
    countryName: 'Niger',
    regions: {'africa'},
    iso2Code: 'ne',
    intlDialCode: '227',
    flag: '🇳🇪',
  ),
  const CountryModel(
    countryName: 'Nigeria',
    regions: {'africa'},
    iso2Code: 'ng',
    intlDialCode: '234',
    flag: '🇳🇬',
  ),
  const CountryModel(
    countryName: 'North Korea',
    regions: {'asia'},
    iso2Code: 'kp',
    intlDialCode: '850',
    flag: '🇰🇵',
  ),
  const CountryModel(
    countryName: 'Norway',
    regions: {'europe', 'baltic'},
    iso2Code: 'no',
    intlDialCode: '47',
    flag: '🇳🇴',
    format: '... .. ...',
  ),
  const CountryModel(
    countryName: 'Oman',
    regions: {'middle-east'},
    iso2Code: 'om',
    intlDialCode: '968',
    flag: '🇴🇲',
  ),
  const CountryModel(
    countryName: 'Pakistan',
    regions: {'asia'},
    iso2Code: 'pk',
    intlDialCode: '92',
    flag: '🇵🇰',
    format: '...-.......',
  ),
  const CountryModel(
    countryName: 'Palau',
    regions: {'oceania'},
    iso2Code: 'pw',
    intlDialCode: '680',
    flag: '🇵🇼',
  ),
  const CountryModel(
    countryName: 'Palestine',
    regions: {'middle-east'},
    iso2Code: 'ps',
    intlDialCode: '970',
    flag: '🇵🇸',
  ),
  const CountryModel(
    countryName: 'Panama',
    regions: {'america', 'central-america'},
    iso2Code: 'pa',
    intlDialCode: '507',
    flag: '🇵🇦',
  ),
  const CountryModel(
    countryName: 'Papua New Guinea',
    regions: {'oceania'},
    iso2Code: 'pg',
    intlDialCode: '675',
    flag: '🇵🇬',
  ),
  const CountryModel(
    countryName: 'Paraguay',
    regions: {'america', 'south-america'},
    iso2Code: 'py',
    intlDialCode: '595',
    flag: '🇵🇾',
  ),
  const CountryModel(
    countryName: 'Peru',
    regions: {'america', 'south-america'},
    iso2Code: 'pe',
    intlDialCode: '51',
    flag: '🇵🇪',
  ),
  const CountryModel(
    countryName: 'Philippines',
    regions: {'asia'},
    iso2Code: 'ph',
    intlDialCode: '63',
    flag: '🇵🇭',
    format: '.... .......',
  ),
  const CountryModel(
    countryName: 'Poland',
    regions: {'europe', 'eu-union', 'baltic'},
    iso2Code: 'pl',
    intlDialCode: '48',
    flag: '🇵🇱',
    format: '...-...-...',
  ),
  const CountryModel(
    countryName: 'Portugal',
    regions: {'europe', 'eu-union'},
    iso2Code: 'pt',
    intlDialCode: '351',
    flag: '🇵🇹',
  ),
  const CountryModel(
    countryName: 'Puerto Rico',
    regions: {'america', 'carribean'},
    iso2Code: 'pr',
    intlDialCode: '1',
    flag: '🇵🇷',
    orderPriority: 3,
    areaCodes: {'787', '939'},
  ),
  const CountryModel(
    countryName: 'Qatar',
    regions: {'middle-east'},
    iso2Code: 'qa',
    intlDialCode: '974',
    flag: '🇶🇦',
  ),
  const CountryModel(
    countryName: 'Romania',
    regions: {'europe', 'eu-union'},
    iso2Code: 'ro',
    intlDialCode: '40',
    flag: '🇷🇴',
  ),
  const CountryModel(
    countryName: 'Russia',
    regions: {'europe', 'asia', 'ex-ussr', 'baltic'},
    iso2Code: 'ru',
    intlDialCode: '7',
    flag: '🇷🇺',
    format: '(...) ...-..-..',
    orderPriority: 0,
  ),
  const CountryModel(
    countryName: 'Rwanda',
    regions: {'africa'},
    iso2Code: 'rw',
    intlDialCode: '250',
    flag: '🇷🇼',
  ),
  const CountryModel(
    countryName: 'Réunion',
    regions: {'africa'},
    iso2Code: 're',
    intlDialCode: '262',
    flag: '🇷🇪',
  ),
  const CountryModel(
    countryName: 'Saint Kitts and Nevis',
    regions: {'america', 'carribean'},
    iso2Code: 'kn',
    intlDialCode: '1869',
    flag: '🇰🇳',
  ),
  const CountryModel(
    countryName: 'Saint Lucia',
    regions: {'america', 'carribean'},
    iso2Code: 'lc',
    intlDialCode: '1758',
    flag: '🇱🇨',
  ),
  const CountryModel(
    countryName: 'Saint Vincent and the Grenadines',
    regions: {'america', 'carribean'},
    iso2Code: 'vc',
    intlDialCode: '1784',
    flag: '🇻🇨',
  ),
  const CountryModel(
    countryName: 'Samoa',
    regions: {'oceania'},
    iso2Code: 'ws',
    intlDialCode: '685',
    flag: '🇼🇸',
  ),
  const CountryModel(
    countryName: 'San Marino',
    regions: {'europe'},
    iso2Code: 'sm',
    intlDialCode: '378',
    flag: '🇸🇲',
  ),
  const CountryModel(
    countryName: 'Saudi Arabia',
    regions: {'middle-east'},
    iso2Code: 'sa',
    intlDialCode: '966',
    flag: '🇸🇦',
  ),
  const CountryModel(
    countryName: 'Senegal',
    regions: {'africa'},
    iso2Code: 'sn',
    intlDialCode: '221',
    flag: '🇸🇳',
  ),
  const CountryModel(
    countryName: 'Serbia',
    regions: {'europe', 'ex-yugos'},
    iso2Code: 'rs',
    intlDialCode: '381',
    flag: '🇷🇸',
  ),
  const CountryModel(
    countryName: 'Seychelles',
    regions: {'africa'},
    iso2Code: 'sc',
    intlDialCode: '248',
    flag: '🇸🇨',
  ),
  const CountryModel(
    countryName: 'Sierra Leone',
    regions: {'africa'},
    iso2Code: 'sl',
    intlDialCode: '232',
    flag: '🇸🇱',
  ),
  const CountryModel(
    countryName: 'Singapore',
    regions: {'asia'},
    iso2Code: 'sg',
    intlDialCode: '65',
    flag: '🇸🇬',
    format: '....-....',
  ),
  const CountryModel(
    countryName: 'Slovakia',
    regions: {'europe', 'eu-union'},
    iso2Code: 'sk',
    intlDialCode: '421',
    flag: '🇸🇰',
  ),
  const CountryModel(
    countryName: 'Slovenia',
    regions: {'europe', 'eu-union', 'ex-yugos'},
    iso2Code: 'si',
    intlDialCode: '386',
    flag: '🇸🇮',
  ),
  const CountryModel(
    countryName: 'Solomon Islands',
    regions: {'oceania'},
    iso2Code: 'sb',
    intlDialCode: '677',
    flag: '🇸🇧',
  ),
  const CountryModel(
    countryName: 'Somalia',
    regions: {'africa'},
    iso2Code: 'so',
    intlDialCode: '252',
    flag: '🇸🇴',
  ),
  const CountryModel(
    countryName: 'South Africa',
    regions: {'africa'},
    iso2Code: 'za',
    intlDialCode: '27',
    flag: '🇿🇦',
  ),
  const CountryModel(
    countryName: 'South Korea',
    regions: {'asia'},
    iso2Code: 'kr',
    intlDialCode: '82',
    flag: '🇰🇷',
    format: '... .... ....',
  ),
  const CountryModel(
    countryName: 'South Sudan',
    regions: {'africa', 'north-africa'},
    iso2Code: 'ss',
    intlDialCode: '211',
    flag: '🇸🇸',
  ),
  const CountryModel(
    countryName: 'Spain',
    regions: {'europe', 'eu-union'},
    iso2Code: 'es',
    intlDialCode: '34',
    flag: '🇪🇸',
    format: '... ... ...',
  ),
  const CountryModel(
    countryName: 'Sri Lanka',
    regions: {'asia'},
    iso2Code: 'lk',
    intlDialCode: '94',
    flag: '🇱🇰',
  ),
  const CountryModel(
    countryName: 'Sudan',
    regions: {'africa'},
    iso2Code: 'sd',
    intlDialCode: '249',
    flag: '🇸🇩',
  ),
  const CountryModel(
    countryName: 'Suriname',
    regions: {'america', 'south-america'},
    iso2Code: 'sr',
    intlDialCode: '597',
    flag: '🇸🇷',
  ),
  const CountryModel(
    countryName: 'Swaziland',
    regions: {'africa'},
    iso2Code: 'sz',
    intlDialCode: '268',
    flag: '🇸🇿',
  ),
  const CountryModel(
    countryName: 'Sweden',
    regions: {'europe', 'eu-union', 'baltic'},
    iso2Code: 'se',
    intlDialCode: '46',
    flag: '🇸🇪',
    format: '(...) ...-...',
  ),
  const CountryModel(
    countryName: 'Switzerland',
    regions: {'europe'},
    iso2Code: 'ch',
    intlDialCode: '41',
    flag: '🇨🇭',
    format: '.. ... .. ..',
  ),
  const CountryModel(
    countryName: 'Syria',
    regions: {'middle-east'},
    iso2Code: 'sy',
    intlDialCode: '963',
    flag: '🇸🇾',
  ),
  const CountryModel(
    countryName: 'São Tomé and Príncipe',
    regions: {'africa'},
    iso2Code: 'st',
    intlDialCode: '239',
    flag: '🇸🇹',
  ),
  const CountryModel(
    countryName: 'Taiwan',
    regions: {'asia'},
    iso2Code: 'tw',
    intlDialCode: '886',
    flag: '🇹🇼',
  ),
  const CountryModel(
    countryName: 'Tajikistan',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'tj',
    intlDialCode: '992',
    flag: '🇹🇯',
  ),
  const CountryModel(
    countryName: 'Tanzania',
    regions: {'africa'},
    iso2Code: 'tz',
    intlDialCode: '255',
    flag: '🇹🇿',
  ),
  const CountryModel(
    countryName: 'Thailand',
    regions: {'asia'},
    iso2Code: 'th',
    intlDialCode: '66',
    flag: '🇹🇭',
  ),
  const CountryModel(
    countryName: 'Timor-Leste',
    regions: {'asia'},
    iso2Code: 'tl',
    intlDialCode: '670',
    flag: '🇹🇱',
  ),
  const CountryModel(
    countryName: 'Togo',
    regions: {'africa'},
    iso2Code: 'tg',
    intlDialCode: '228',
    flag: '🇹🇬',
  ),
  const CountryModel(
    countryName: 'Tonga',
    regions: {'oceania'},
    iso2Code: 'to',
    intlDialCode: '676',
    flag: '🇹🇴',
  ),
  const CountryModel(
    countryName: 'Trinidad and Tobago',
    regions: {'america', 'carribean'},
    iso2Code: 'tt',
    intlDialCode: '1868',
    flag: '🇹🇹',
  ),
  const CountryModel(
    countryName: 'Tunisia',
    regions: {'africa', 'north-africa'},
    iso2Code: 'tn',
    intlDialCode: '216',
    flag: '🇹🇳',
  ),
  const CountryModel(
    countryName: 'Turkey',
    regions: {'europe'},
    iso2Code: 'tr',
    intlDialCode: '90',
    flag: '🇹🇷',
    format: '... ... .. ..',
  ),
  const CountryModel(
    countryName: 'Turkmenistan',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'tm',
    intlDialCode: '993',
    flag: '🇹🇲',
  ),
  const CountryModel(
    countryName: 'Tuvalu',
    regions: {'asia'},
    iso2Code: 'tv',
    intlDialCode: '688',
    flag: '🇹🇻',
  ),
  const CountryModel(
    countryName: 'Uganda',
    regions: {'africa'},
    iso2Code: 'ug',
    intlDialCode: '256',
    flag: '🇺🇬',
  ),
  const CountryModel(
    countryName: 'Ukraine',
    regions: {'europe', 'ex-ussr'},
    iso2Code: 'ua',
    intlDialCode: '380',
    flag: '🇺🇦',
    format: '(..) ... .. ..',
  ),
  const CountryModel(
    countryName: 'United Arab Emirates',
    regions: {'middle-east'},
    iso2Code: 'ae',
    intlDialCode: '971',
    flag: '🇦🇪',
  ),
  const CountryModel(
    countryName: 'United Kingdom',
    regions: {'europe', 'eu-union'},
    iso2Code: 'gb',
    intlDialCode: '44',
    flag: '🇬🇧',
    format: '.... ......',
  ),
  const CountryModel(
    countryName: 'United States',
    regions: {'america', 'north-america'},
    iso2Code: 'us',
    intlDialCode: '1',
    flag: '🇺🇸',
    format: '(...) ...-....',
    orderPriority: 0,
    areaCodes: {
      '907',
      '205',
      '251',
      '256',
      '334',
      '479',
      '501',
      '870',
      '480',
      '520',
      '602',
      '623',
      '928',
      '209',
      '213',
      '310',
      '323',
      '408',
      '415',
      '510',
      '530',
      '559',
      '562',
      '619',
      '626',
      '650',
      '661',
      '707',
      '714',
      '760',
      '805',
      '818',
      '831',
      '858',
      '909',
      '916',
      '925',
      '949',
      '951',
      '303',
      '719',
      '970',
      '203',
      '860',
      '202',
      '302',
      '239',
      '305',
      '321',
      '352',
      '386',
      '407',
      '561',
      '727',
      '772',
      '813',
      '850',
      '863',
      '904',
      '941',
      '954',
      '229',
      '404',
      '478',
      '706',
      '770',
      '912',
      '808',
      '319',
      '515',
      '563',
      '641',
      '712',
      '208',
      '217',
      '309',
      '312',
      '618',
      '630',
      '708',
      '773',
      '815',
      '847',
      '219',
      '260',
      '317',
      '574',
      '765',
      '812',
      '316',
      '620',
      '785',
      '913',
      '270',
      '502',
      '606',
      '859',
      '225',
      '318',
      '337',
      '504',
      '985',
      '413',
      '508',
      '617',
      '781',
      '978',
      '301',
      '410',
      '207',
      '231',
      '248',
      '269',
      '313',
      '517',
      '586',
      '616',
      '734',
      '810',
      '906',
      '989',
      '218',
      '320',
      '507',
      '612',
      '651',
      '763',
      '952',
      '314',
      '417',
      '573',
      '636',
      '660',
      '816',
      '228',
      '601',
      '662',
      '406',
      '252',
      '336',
      '704',
      '828',
      '910',
      '919',
      '701',
      '308',
      '402',
      '603',
      '201',
      '609',
      '732',
      '856',
      '908',
      '973',
      '505',
      '575',
      '702',
      '775',
      '212',
      '315',
      '516',
      '518',
      '585',
      '607',
      '631',
      '716',
      '718',
      '845',
      '914',
      '216',
      '330',
      '419',
      '440',
      '513',
      '614',
      '740',
      '937',
      '405',
      '580',
      '918',
      '503',
      '541',
      '215',
      '412',
      '570',
      '610',
      '717',
      '724',
      '814',
      '401',
      '803',
      '843',
      '864',
      '605',
      '423',
      '615',
      '731',
      '865',
      '901',
      '931',
      '210',
      '214',
      '254',
      '281',
      '325',
      '361',
      '409',
      '432',
      '512',
      '713',
      '806',
      '817',
      '830',
      '903',
      '915',
      '936',
      '940',
      '956',
      '972',
      '979',
      '435',
      '801',
      '276',
      '434',
      '540',
      '703',
      '757',
      '804',
      '802',
      '206',
      '253',
      '360',
      '425',
      '509',
      '262',
      '414',
      '608',
      '715',
      '920',
      '304',
      '307',
    },
  ),
  const CountryModel(
    countryName: 'Uruguay',
    regions: {'america', 'south-america'},
    iso2Code: 'uy',
    intlDialCode: '598',
    flag: '🇺🇾',
  ),
  const CountryModel(
    countryName: 'Uzbekistan',
    regions: {'asia', 'ex-ussr'},
    iso2Code: 'uz',
    intlDialCode: '998',
    flag: '🇺🇿',
    format: '.. ... .. ..',
  ),
  const CountryModel(
    countryName: 'Vanuatu',
    regions: {'oceania'},
    iso2Code: 'vu',
    intlDialCode: '678',
    flag: '🇻🇺',
  ),
  const CountryModel(
    countryName: 'Vatican City',
    regions: {'europe'},
    iso2Code: 'va',
    intlDialCode: '39',
    flag: '🇻🇦',
    format: '.. .... ....',
    orderPriority: 1,
  ),
  const CountryModel(
    countryName: 'Venezuela',
    regions: {'america', 'south-america'},
    iso2Code: 've',
    intlDialCode: '58',
    flag: '🇻🇪',
  ),
  const CountryModel(
    countryName: 'Vietnam',
    regions: {'asia'},
    iso2Code: 'vn',
    intlDialCode: '84',
    flag: '🇻🇳',
  ),
  const CountryModel(
    countryName: 'Yemen',
    regions: {'middle-east'},
    iso2Code: 'ye',
    intlDialCode: '967',
    flag: '🇾🇪',
  ),
  const CountryModel(
    countryName: 'Zambia',
    regions: {'africa'},
    iso2Code: 'zm',
    intlDialCode: '260',
    flag: '🇿🇲',
  ),
  const CountryModel(
    countryName: 'Zimbabwe',
    regions: {'africa'},
    iso2Code: 'zw',
    intlDialCode: '263',
    flag: '🇿🇼',
  ),
];
