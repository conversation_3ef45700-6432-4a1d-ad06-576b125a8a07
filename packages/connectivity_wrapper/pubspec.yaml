name: connectivity_wrapper
description: A new Flutter package which provides network-aware widgets.It allows Flutter apps provide feedback on your app when it's not connected to it, or when there's no connection.
version: 1.2.6
homepage: https://github.com/nonstopio/flutter_forge/tree/main/packages/connectivity_wrapper
documentation: https://github.com/nonstopio/flutter_forge/tree/main/packages/connectivity_wrapper
repository: https://github.com/nonstopio/flutter_forge/tree/main/packages/connectivity_wrapper
issue_tracker: https://github.com/nonstopio/flutter_forge/issues
screenshots:
  - description: Icon Image
    path: screenshots/icon.png

environment:
  sdk: ">=2.18.4 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.2
  connectivity_plus: ^6.1.2 # To check connectivity for web only

dev_dependencies:
  flutter_test:
    sdk: flutter