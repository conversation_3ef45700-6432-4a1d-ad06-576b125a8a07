## 1.2.1

 - **DOCS**: add NonStop branding to README files across project.

## 1.2.0

 - **FIX**: lint fixed.
 - **FEAT**(ns_utils): Moved ns_utils to mono repo from https://github.com/ProjectAJ14/ns_utils.

## [1.1.2]
- Add String extensions for `firstNChars`, `lastNChars`, `truncateMiddle`.
- Add Map extensions for `getSDMap` which returns `Map<String, dynamic>`.
- Add Map extensions for `getBoolWithDefaultValue` which returns bool.
- Add List\<String> extensions for check `containWithoutCase` which returns bool.

## [1.1.1]
- Updated plugins

## [1.1.0]
- Use device_info_plus instead of device_info
- Use package_info_plus instead of package_info
- Updated String extensions 
- Added export for other packages

## [1.0.0]
- Minor fixes on null safety. Thanks [Rajan](https://github.com/rajan-nonstopio)

## [1.0.0-nullsafety]
- Migrating to null safety

## [0.0.7]
- Added Function Extensions
- Added more Map Extensions
- Added import_sorter
- Added analysis_options.yaml i.e to follow effective_dart
- Updated README.md

## [0.0.6]
- Updated dependencies
- Added more extensions

## [0.0.5]
- Added SharedPreferences Service
- Added DeviceService
- Added NSUtils.init

## [0.0.3]
- Updated README.md

## [0.0.3]
- Updated README.md

## [0.0.2] - Custom error widget.
- Added CustomErrorWidget
- Updated README.md
- Added ValidatorUtil
- Added setFocus in ContextExtensions

## [0.0.1] - initial release.
- `BuildContext` extensions 🦾
- `DateTime` extensions 🦾
- `Map` extensions 🦾
- `String` extensions 🦾
- `double` extensions 🦾
- `int` extensions 🦾
- `List` extensions 🦾
- `conversion` methods 🦾
- `Widget` extensions 🦾
- `Spacers` spacers widgets 🦾
- `Sizes` responsive app 🦾







