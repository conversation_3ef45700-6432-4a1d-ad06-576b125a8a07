<p align="center">
  <a href="https://nonstopio.com">
    <img src="https://github.com/nonstopio.png" alt="Nonstop Logo" height="128" />
  </a>
  <h1 align="center">NonStop</h1>
  <p align="center">Digital Product Development Experts for Startups & Enterprises</p>
  <p align="center">
    <a href="https://nonstopio.com/about">About</a> |
    <a href="https://nonstopio.com">Website</a>
  </p>
</p>

# ns_firebase_utils

[![Build Status](https://img.shields.io/pub/v/ns_firebase_utils.svg)](https://github.com/nonstopio/flutter_forge/tree/main/packages/ns_firebase_utils)
[![License: MIT](https://img.shields.io/badge/license-MIT-blue.svg)](https://opensource.org/licenses/MIT)

🚀 This package provides a comprehensive set of methods and extensions for Firebase, simplifying its integration into your Flutter projects while also adding valuable functionality.

## Getting Started

1. Open your `pubspec.yaml` file.
2. Add `ns_firebase_utils` as a dependency, replacing `[version]` with the latest release:

```yaml
dependencies:
  flutter:
    sdk: flutter
  ns_firebase_utils: ^[version]
```

3. Run `flutter pub get` or click the "Packages get" button in your IDE.

## Importing the Package

```dart
import 'package:ns_firebase_utils/src.dart';
```

## What's Included

This package offers a single plugin that conveniently adds all necessary Firebase dependencies to your project:

```yaml
  cloud_firestore: ^5.5.0
  cloud_functions: ^5.1.5
  fake_cloud_firestore: ^3.1.0
  firebase_analytics: ^11.3.5
  firebase_auth: ^5.3.3
  firebase_core: ^3.8.0
  firebase_crashlytics: ^4.1.5
  firebase_dynamic_links: ^6.0.10
  firebase_messaging: ^15.1.5
  firebase_remote_config: ^5.1.5
  firebase_storage: ^12.3.6
```

Additionally, it provides a powerful set of methods to accelerate your development process.

## Contributing

We welcome contributions in various forms:

- Proposing new features or enhancements.
- Reporting and fixing bugs.
- Engaging in discussions to help make decisions.
- Improving documentation, as it is essential.
- Sending Pull Requests is greatly appreciated!

A big thank you to all our contributors! 🙌

<br></br>
<div align="center">
  <a href="https://github.com/nonstopio/flutter_forge/graphs/contributors">
    <img src="https://contrib.rocks/image?repo=nonstopio/flutter_forge"  alt="contributors"/>
  </a>
</div>