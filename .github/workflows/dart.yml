name: Dart

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        # All the packages are specified by their name and not path.
        # This is because the package is passed to the 'melos exec' command
        package:
          - connectivity_wrapper
          - contact_permission
          - ns_firebase_utils
          - ns_utils
          - timer_button
          - nonstop_cli
        include:
          - package: connectivity_wrapper
            path: packages
            test: false
          - package: contact_permission
            path: plugins
            test: false
          - package: ns_firebase_utils
            path: packages
            test: false
          - package: ns_utils
            path: packages
            test: false
          - package: timer_button
            path: packages
            test: false
          - package: nonstop_cli
            path: packages
            test: false
          - package: ns_intl_phone_input
            path: packages
            test: true
    steps:
      - uses: actions/checkout@v3

      - uses: subosito/flutter-action@v2
      - name: Install dependencies
        run: |
          dart pub global activate melos
          dart pub get
          dart run melos bs

      - name: Format package
        run: melos exec --scope="${{ matrix.package }}" -- dart format . --set-exit-if-changed

      - name: Analyze package
        run: melos exec --scope="${{ matrix.package }}" -- dart analyze --fatal-infos

      - name: Run tests
        if: matrix.test
        run: melos exec --scope="${{ matrix.package }}" -- flutter test --coverage

      - name: Check Code Coverage
        if: matrix.test
        uses: VeryGoodOpenSource/very_good_coverage@v3.0.0
        with:
          path: ${{ matrix.path }}/${{ matrix.package }}/coverage/lcov.info
          min_coverage: 90

      - name: Upload coverage reports to Codecov
        if: matrix.test
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          flags: ${{ matrix.package }}
          files: ${{ matrix.path }}/${{ matrix.package }}/coverage/lcov.info
