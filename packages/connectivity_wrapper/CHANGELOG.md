## 1.2.6

 - **DOCS**: add NonStop branding to README files across project.

## 1.2.5

 - **FIX**(contact_permission): lint fixed.
 - **DOCS**(connectivity_wrapper): Add comprehensive documentation and comments to improve code readability.

## 1.2.4

 - **DOCS**(connectivity_wrapper): Remove example images from README.

## 1.2.3

 - **DOCS**(connectivity_wrapper): Update README image styling with responsive aspect ratio.
 - **DOCS**(connectivity_wrapper): Enhance README with improved formatting and visual examples.

## 1.2.2

 - **DOCS**(connectivity_wrapper): Improve README markdown formatting and headers.

## 1.2.1

 - **FIX**(connectivity_wrapper): Update screenshot description in pubspec.yaml.

## 1.2.0

 - **FIX**(connectivity_wrapper): Improve web connection check with additional connectivity types.
 - **FEAT**(connectivity_wrapper): Moved `connectivity_wrapper` to mono repo.
 - **DOCS**(connectivity_wrapper): Update README badge and formatting.

## [1.1.4] - Dependency Update

- Updated connectivity_plus: `^5.0.1`

## [1.1.3] - #18 Issue Resolved

- Exported `AddressCheckOptions`

## [1.1.2] - Maintenance issues and suggestions.

- Updated AddressCheckOptions list
- Code Refactor i.e added flutter_lints

## [1.1.1] - Updated plugins

## [1.1.0] - Updated plugins

- Resolved [#12](https://github.com/ProjectAj14/connectivity_wrapper/issues/12)
- Updated example for showing the widget for the entire app

## [1.0.6] - Maintenance issues and suggestions.

- Resolved [#6](https://github.com/ProjectAj14/connectivity_wrapper/issues/6)

## [1.0.5] - Maintenance issues and suggestions.

- Migrating to null safety and updated provider to: ^5.0.0 [#7](https://github.com/ProjectAj14/connectivity_wrapper/issues/7)

## [1.0.4] - Maintenance issues and suggestions.

- Added New ConnectivityScreenWrapper for [#1](https://github.com/ProjectAj14/connectivity_wrapper/issues/1)
- Updated to provider: ^4.3.2 [#2](https://github.com/ProjectAj14/connectivity_wrapper/issues/2)
- Exposed `lastStatus` as an API

## [1.0.3] - Maintenance issues and suggestions.

- Bumping version number, so pub.dev can accept the package
- Exposed `ConnectivityWrapper` as an API

## [1.0.2] - Health suggestions.

- Health suggestions.

## [1.0.1] - Updated Connectivity Service.

- Updated Connectivity Service.

## [1.0.1] - initial release.

- Initial release.
